import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ModelConfigDto } from '@modules/agent/admin/dto/common';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';

/**
 * DTO cho việc cập nhật agent base
 */
export class UpdateAgentBaseDto {
  /**
   * Tên hiển thị của agent
   */
  @ApiPropertyOptional({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  @IsString()
  @IsOptional()
  name?: string;

  /**
   * MIME type của avatar (nếu cần cập nhật avatar)
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar (nếu cần cập nhật avatar)',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * <PERSON><PERSON><PERSON> h<PERSON>nh model AI
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> hình model AI',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  @IsOptional()
  modelConfig?: ModelConfigDto;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example:
      'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string | null;

  /**
   * ID của vector store
   */
  @ApiPropertyOptional({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;

  /**
   * Trạng thái của agent
   */
  @ApiPropertyOptional({
    description: 'Trạng thái của agent',
    enum: AgentStatusEnum,
  })
  @IsString()
  @IsOptional()
  status?: AgentStatusEnum;

  /**
   * Trạng thái active của agent base
   */
  @ApiPropertyOptional({
    description: 'Trạng thái active của agent base',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
