import { Injectable } from '@nestjs/common';
import { AdminGroupToolRepository } from '@/modules/tools/repositories';
import { GroupToolDto } from '../dto/type-agent';

/**
 * Helper class để xử lý các thao tác liên quan đến nhóm công cụ
 */
@Injectable()
export class GroupToolHelper {
  constructor(
    private readonly adminGroupToolRepository: AdminGroupToolRepository,
  ) {}

  /**
   * Đếm số lượng nhóm công cụ của loại agent
   * @param typeAgentId ID của loại agent
   * @returns Số lượng nhóm công cụ
   */
  async countGroupToolsByTypeAgentId(typeAgentId: number): Promise<number> {
    // Tạo query builder để đếm số lượng nhóm công cụ
    return await this.adminGroupToolRepository
      .createQueryBuilder('groupTool')
      .innerJoin(
        'admin_group_tools_type_agents',
        'tagt',
        'tagt.group_id = groupTool.id',
      )
      .where('tagt.type_agent_id = :typeAgentId', { typeAgentId })
      .getCount();
  }

  /**
   * Lấy danh sách nhóm công cụ của loại agent
   * @param typeAgentId ID của loại agent
   * @returns Danh sách nhóm công cụ
   */
  async getGroupToolsByTypeAgentId(
    typeAgentId: number,
  ): Promise<GroupToolDto[]> {
    // Tạo query builder để lấy danh sách nhóm công cụ
    const groupTools = await this.adminGroupToolRepository
      .createQueryBuilder('groupTool')
      .innerJoin(
        'admin_group_tools_type_agents',
        'tagt',
        'tagt.group_id = groupTool.id',
      )
      .where('tagt.type_agent_id = :typeAgentId', { typeAgentId })
      .select([
        'groupTool.id as id',
        'groupTool.name as name',
        'groupTool.description as description',
        '(SELECT COUNT(*) FROM admin_group_tools_type_agents WHERE group_id = groupTool.id) as tool_count',
      ])
      .getRawMany();

    // Chuyển đổi kết quả sang DTO
    return groupTools.map((gt) => ({
      id: gt.id,
      name: gt.name,
      description: gt.description,
      toolCount: parseInt(gt.tool_count),
    }));
  }

  /**
   * Liên kết loại agent với các nhóm công cụ
   * @param typeAgentId ID của loại agent
   * @param groupToolIds Danh sách ID của các nhóm công cụ
   */
  async linkGroupTools(
    typeAgentId: number,
    groupToolIds: number[],
  ): Promise<void> {
    // Tạo query builder để xóa các liên kết cũ
    await this.adminGroupToolRepository
      .createQueryBuilder()
      .delete()
      .from('admin_group_tools_type_agents')
      .where('type_agent_id = :typeAgentId', { typeAgentId })
      .execute();

    // Thêm các liên kết mới
    for (const groupToolId of groupToolIds) {
      await this.adminGroupToolRepository
        .createQueryBuilder()
        .insert()
        .into('admin_group_tools_type_agents')
        .values({ type_agent_id: typeAgentId, group_tool_id: groupToolId })
        .execute();
    }
  }
}
