import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TypeAgentConfig } from '../../../interfaces/type-agent-config.interface';

/**
 * DTO cho cấu hình loại agent
 * Tự động đồng bộ với TypeAgentConfig interface
 */
export class TypeAgentConfigDto implements TypeAgentConfig {
  /**
   * <PERSON><PERSON> hồ sơ không
   */
  @ApiProperty({
    description: '<PERSON><PERSON> hồ sơ không - Cho phép agent có thông tin hồ sơ cá nhân',
    example: true,
  })
  @IsBoolean()
  hasProfile: boolean;

  /**
   * <PERSON><PERSON> đầu ra không
   */
  @ApiProperty({
    description: '<PERSON><PERSON> đầu ra không - Cho phép agent tạo ra các output/kết quả',
    example: true,
  })
  @IsBoolean()
  hasOutput: boolean;

  /**
   * <PERSON><PERSON> chuyển đổi không
   */
  @ApiProperty({
    description: 'Có chuyển đổi không - Cho phép agent thực hiện các conversion/chuyển đổi dữ liệu',
    example: false,
  })
  @IsBoolean()
  hasConversion: boolean;

  /**
   * Có tài nguyên không
   */
  @ApiProperty({
    description: 'Có tài nguyên không - Cho phép agent sử dụng các resources/tài nguyên',
    example: true,
  })
  @IsBoolean()
  hasResources: boolean;

  /**
   * Có chiến lược không
   */
  @ApiProperty({
    description: 'Có chiến lược không - Cho phép agent sử dụng các strategy/chiến lược',
    example: true,
  })
  @IsBoolean()
  hasStrategy: boolean;

  /**
   * Có đa agent không
   */
  @ApiProperty({
    description: 'Có đa agent không - Cho phép agent làm việc với nhiều agent khác',
    example: true,
  })
  @IsBoolean()
  hasMultiAgent: boolean;
}

/**
 * DTO cho việc tạo loại agent mới
 */
export class CreateTypeAgentDto {
  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Custom Agent',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * Mô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent tùy chỉnh của người dùng',
  })
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * Cấu hình mặc định cho loại agent
   */
  @ApiProperty({
    description: 'Cấu hình mặc định cho loại agent - Tự động đồng bộ với TypeAgentConfig interface',
    type: TypeAgentConfigDto,
    example: {
      hasProfile: true,
      hasOutput: true,
      hasConversion: false,
      hasResources: true,
      hasStrategy: true,
      hasMultiAgent: false,
    },
  })
  @ValidateNested()
  @Type(() => TypeAgentConfigDto)
  config: TypeAgentConfigDto;

  /**
   * Danh sách ID của các nhóm công cụ
   */
  @ApiProperty({
    description: 'Danh sách ID của các nhóm công cụ',
    example: [1, 2],
    type: [Number],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  groupToolIds: number[];
}
