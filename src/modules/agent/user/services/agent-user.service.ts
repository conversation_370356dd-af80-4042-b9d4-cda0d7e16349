import { Api<PERSON>eyEncryptionHelper } from '@/modules/model-training/helpers/api-key-encryption.helper';
import { AppException } from '@common/exceptions';
import { generateS3Key } from '@common/helpers';
import { PaginatedResult } from '@common/response';
import { AgentStatusEnum } from '@modules/agent/constants';
import { Agent, AgentUser } from '@modules/agent/entities';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';
import {
  AgentMediaRepository,
  AgentProductRepository,
  AgentRankRepository,
  AgentRepository,
  AgentUrlRepository,
  AgentUserRepository,
  TypeAgentRepository
} from '@modules/agent/repositories';
import { AiProviderHelper } from '@shared/services/ai/helpers/ai-provider.helper';

import { UserProductRepository } from '@/modules/business/repositories';
import { BaseModelAdminService } from '@/modules/model-training/admin/services';
import { VectorStoreRepository } from '@modules/data/knowledge-files/repositories';
import { MediaRepository } from '@modules/data/media/repositories';
import { UrlRepository } from '@modules/data/url/repositories';
import { UserProviderModelRepository } from '@modules/model-training/repositories/user-provider-model.repository';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { TimeIntervalEnum } from '@shared/utils';
import { CategoryFolderEnum } from '@shared/utils/generators/s3-key-generator.util';
import { In } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { GenderUtils } from '../../constants/gender.enum';
import { ProfileAgent } from '../../interfaces/profile-agent.interface';
import {
  AgentDetailDto,
  AgentListItemDto,
  AgentQueryDto,
  AgentSimpleListDto,
  AgentStatisticsQueryDto,
  AgentStatisticsResponseDto,
  CreateAgentDto,
  CreateAgentResponseDto,
  ModelConfigDto,
  ProfileDto,
  UpdateAgentDto,
  UpdateAgentResponseDto,
  UpdateAgentTypeDto,
  UpdateAgentVectorStoreDto
} from '../dto';
import { AgentMapper } from '../mappers/agent.mapper';
import { TypeProviderUtil } from './../../../model-training/constants/type-provider.enum';

/**
 * Service xử lý các thao tác liên quan đến agent cho người dùng
 */
@Injectable()
export class AgentUserService {
  private readonly logger = new Logger(AgentUserService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly agentUserRepository: AgentUserRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly agentMediaRepository: AgentMediaRepository,
    private readonly agentUrlRepository: AgentUrlRepository,
    private readonly agentProductRepository: AgentProductRepository,
    private readonly vectorStoreRepository: VectorStoreRepository,
    private readonly userProviderModelRepository: UserProviderModelRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly urlRepository: UrlRepository,
    private readonly userProductRepository: UserProductRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly baseModelAdminService: BaseModelAdminService,
    private readonly apiKeyEncryptionHelper: ApiKeyEncryptionHelper,
    private readonly aiProviderHelper: AiProviderHelper,
    private readonly agentRankRepository: AgentRankRepository,
  ) { }

  /**
   * Lấy danh sách agent đơn giản của người dùng (chỉ id, avatar, name)
   * @param userId ID của người dùng
   * @returns Danh sách agent đơn giản
   */
  async getSimpleAgentList(userId: number): Promise<AgentSimpleListDto[]> {
    try {
      // Lấy danh sách agent từ repository
      const agents = await this.agentRepository.findSimpleListByUserId(userId);

      // Chuyển đổi sang DTO và gán CDN URL cho avatar
      return agents.map(agent => ({
        id: agent.id,
        avatar: agent.avatar ? this.cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_DAY) || undefined : undefined,
        name: agent.name,
      }));
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách agent đơn giản: ${error.message}`);
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_LIST_QUERY_FAILED,
        error.message,
      );
    }
  }

  /**
   * Lấy danh sách agent của người dùng
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent có phân trang
   */
  async getAgents(
    userId: number,
    queryDto: AgentQueryDto,
  ): Promise<PaginatedResult<AgentListItemDto>> {
    try {
      // Sử dụng repository để lấy danh sách agent
      const result = await this.agentUserRepository.findPaginated(
        userId,
        queryDto,
      );

      // Lấy danh sách tất cả cấp bậc
      const ranks = await this.agentRankRepository.findAll();

      // Chuyển đổi kết quả sang DTO
      const agentItems = await Promise.all(result.items.map(async (item: any) => {
        try {
          // Tìm cấp bậc phù hợp dựa trên exp
          const exp = item.exp || 0;
          const rank = ranks.find(r => exp >= r.minExp && exp < r.maxExp) || ranks[0];

          // Sử dụng AgentMapper để chuyển đổi sang DTO
          return AgentMapper.toListItemDto(item, this.cdnService, rank);
        } catch (error) {
          this.logger.error(`Lỗi khi xử lý thông tin agent ${item.id}: ${error.message}`);

          // Trả về thông tin cơ bản nếu có lỗi
          return {
            id: item.id,
            name: item.name,
            avatar: item.avatar,
            typeId: item.typeId || 0,
            typeName: item.typeName || '',
            active: item.active || false,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            exp: item.exp || 0,
            expMax: 100,
            level: 1,
            badge_url: '',
            model_id: item.modelConfig?.modelId || '',
          };
        }
      }));

      return {
        items: agentItems,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách agent: ${error.message}`);
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_LIST_QUERY_FAILED,
        error.message,
      );
    }
  }

  /**
   * Lấy chi tiết agent
   * @param id ID của agent
   * @param userId ID của người dùng
   * @returns Chi tiết agent
   */
  async getAgentDetail(id: string, userId: number): Promise<AgentDetailDto> {
    try {
      // Sử dụng repository để lấy thông tin agent và agent user
      const result = await this.agentUserRepository.findAgentByIdAndUserId(
        id,
        userId,
      );
      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      const { agent, agentUser } = result;

      // Chuyển đổi kết quả sang DTO
      return await this.mapToAgentDetailDto(agent, agentUser);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy chi tiết agent: ${error.message}`);
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_DETAIL_FAILED,
        error.message,
      );
    }
  }

  /**
   * Tạo agent mới
   * @param userId ID của người dùng
   * @param createDto Thông tin agent mới
   * @returns Thông tin tạo agent thành công
   */
  @Transactional()
  async createAgent(
    userId: number,
    createDto: CreateAgentDto,
  ): Promise<CreateAgentResponseDto> {
    try {
      // Kiểm tra loại agent có tồn tại không
      const typeAgent = await this.typeAgentRepository.findById(
        createDto.typeId,
      );
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Kiểm tra quyền truy cập loại agent
      if (typeAgent.userId && typeAgent.userId !== userId && typeAgent.status !== 'APPROVED') {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Kiểm tra cấu hình của type agent
      try {
        await this.validateTypeAgentConfig(createDto.typeId);
      } catch (error) {
        if (error instanceof AppException) {
          throw error;
        }
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND, error.message);
      }

      // Kiểm tra model và provider_id
      if (createDto.modelConfig && typeof createDto.modelConfig === 'object') {

        // Kiểm tra model dựa trên provider_id
        try {
          await this.validateModel(
            createDto.modelConfig.modelId,
            createDto.modelConfig.provider_id
          );
        } catch (error) {
          if (error instanceof AppException) {
            throw error;
          }
          throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED, error.message);
        }
      }

      // Tạo agent mới
      const modelConfig: ModelConfig = {
        modelId: createDto.modelConfig.modelId,
        provider_id: createDto.modelConfig.provider_id || null,
        temperature: createDto.modelConfig.temperature,
        top_p: createDto.modelConfig.top_p,
        top_k: createDto.modelConfig.top_k,
        max_tokens: createDto.modelConfig.max_tokens,
      };

      // Sử dụng agentRepository đã được inject
      const agent = this.agentRepository.create({
        name: createDto.name,
        modelConfig,
        instruction: createDto.instruction,
        status: AgentStatusEnum.PENDING, // Mặc định là PENDING
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      // Tạo S3 key cho avatar nếu có
      let avatarUploadUrl = '';
      if (createDto.avatarMimeType) {
        if (!userId || isNaN(userId)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_S3_KEY, 'ID người dùng không hợp lệ');
        }

        const avatarKey = generateS3Key({
          baseFolder: userId.toString(),
          categoryFolder: CategoryFolderEnum.AGENT,
        });

        // Cập nhật avatar key cho agent
        agent.avatar = avatarKey;

        // Tạo URL để upload avatar
        avatarUploadUrl = await this.s3Service.getDownloadUrl(
          avatarKey,
          TimeIntervalEnum.ONE_HOUR
        );
      }

      // Lưu agent
      const savedAgent = await this.agentRepository.save(agent);

      // Tạo agent user
      // Chuyển đổi từ ProfileDto sang ProfileAgent
      const profileAgent: ProfileAgent = {
        gender: createDto.profile.gender ? GenderUtils.getGender(createDto.profile.gender.toUpperCase()) : undefined,
        dateOfBirth: createDto.profile.dateOfBirth ? new Date(createDto.profile.dateOfBirth) : undefined,
        position: createDto.profile.position,
        education: createDto.profile.education,
        skills: createDto.profile.skills,
        personality: createDto.profile.personality,
        languages: createDto.profile.languages,
        nations: createDto.profile.nations,
      };

      const agentUser = this.agentUserRepository.create({
        id: savedAgent.id,
        userId,
        typeId: createDto.typeId,
        profile: profileAgent,
      });

      // Lưu agent user
      await this.agentUserRepository.save(agentUser);

      // Xử lý tài nguyên media nếu có
      if (createDto.mediaIds && createDto.mediaIds.length > 0) {
        // Kiểm tra tất cả media có tồn tại không
        const existingMedias = await this.mediaRepository.find({
          where: { id: In(createDto.mediaIds) }
        });
        const existingMediaIds = existingMedias.map(media => media.id);
        const nonExistingMediaIds = createDto.mediaIds.filter(id => !existingMediaIds.includes(id));

        if (nonExistingMediaIds.length > 0) {
          throw new AppException(
            AGENT_ERROR_CODES.MEDIA_NOT_FOUND,
            `Không tìm thấy media với ID: ${nonExistingMediaIds.join(', ')}`
          );
        }

        // Kiểm tra quyền truy cập media
        const unauthorizedMedias = existingMedias.filter(media => media.ownedBy !== userId);
        if (unauthorizedMedias.length > 0) {
          throw new AppException(
            AGENT_ERROR_CODES.MEDIA_NOT_FOUND,
            `Không có quyền truy cập media với ID: ${unauthorizedMedias.map(m => m.id).join(', ')}`
          );
        }

        // Thêm từng media vào agent
        for (const mediaId of createDto.mediaIds) {
          await this.agentMediaRepository.addMedia(savedAgent.id, mediaId);
        }
      }

      // Xử lý tài nguyên product nếu có
      if (createDto.productIds && createDto.productIds.length > 0) {
        // Chuyển đổi productIds từ string sang number
        const productIdsNumber = createDto.productIds.map(id => parseInt(id));

        // Kiểm tra tất cả product có tồn tại không
        const existingProducts = await this.userProductRepository.find({
          where: { id: In(productIdsNumber) }
        });

        const existingProductIds = existingProducts.map(product => product.id.toString());
        const nonExistingProductIds = createDto.productIds.filter(id => !existingProductIds.includes(id));

        if (nonExistingProductIds.length > 0) {
          throw new AppException(
            AGENT_ERROR_CODES.PRODUCT_NOT_FOUND,
            `Không tìm thấy sản phẩm với ID: ${nonExistingProductIds.join(', ')}`
          );
        }

        // Kiểm tra quyền truy cập product
        const unauthorizedProducts = existingProducts.filter(product => product.createdBy !== userId);
        if (unauthorizedProducts.length > 0) {
          throw new AppException(
            AGENT_ERROR_CODES.PRODUCT_NOT_FOUND,
            `Không có quyền truy cập sản phẩm với ID: ${unauthorizedProducts.map(p => p.id).join(', ')}`
          );
        }

        // Thêm từng product vào agent
        for (const productId of createDto.productIds) {
          await this.agentProductRepository.addProduct(savedAgent.id, productId);
        }
      }

      // Xử lý tài nguyên url nếu có
      if (createDto.urlIds && createDto.urlIds.length > 0) {
        // Kiểm tra tất cả URL có tồn tại không
        const existingUrls = await this.urlRepository.find({
          where: { id: In(createDto.urlIds) }
        });
        const existingUrlIds = existingUrls.map(url => url.id);
        const nonExistingUrlIds = createDto.urlIds.filter(id => !existingUrlIds.includes(id));

        if (nonExistingUrlIds.length > 0) {
          throw new AppException(
            AGENT_ERROR_CODES.URL_NOT_FOUND,
            `Không tìm thấy URL với ID: ${nonExistingUrlIds.join(', ')}`
          );
        }

        // Kiểm tra quyền truy cập URL
        const unauthorizedUrls = existingUrls.filter(url => url.ownedBy !== userId);
        if (unauthorizedUrls.length > 0) {
          throw new AppException(
            AGENT_ERROR_CODES.URL_NOT_FOUND,
            `Không có quyền truy cập URL với ID: ${unauthorizedUrls.map(u => u.id).join(', ')}`
          );
        }

        // Thêm từng URL vào agent
        for (const urlId of createDto.urlIds) {
          await this.agentUrlRepository.addUrl(savedAgent.id, urlId);
        }
      }

      // Lưu thông tin vector store nếu có
      if (createDto.vectorStoreId) {
        try {
          // Kiểm tra vectorStoreId có tồn tại trong database không
          const vectorStore = await this.vectorStoreRepository.findOne({
            where: { id: createDto.vectorStoreId }
          });

          if (!vectorStore) {
            throw new AppException(
              AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
              `Vector store với ID ${createDto.vectorStoreId} không tồn tại`
            );
          }

          // Nếu vector store tồn tại, lưu vào agent
          savedAgent.vectorStoreId = createDto.vectorStoreId;
          await this.agentRepository.save(savedAgent);
        } catch (error) {
          if (error instanceof AppException) {
            throw error;
          }
          this.logger.error(`Lỗi khi kiểm tra vector store: ${error.message}`);
          throw new AppException(
            AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
            `Không thể xác minh vector store: ${error.message}`
          );
        }
      }

      return {
        id: savedAgent.id,
        avatarUploadUrl,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED, error.message);
    }
  }

  /**
   * Cập nhật agent
   * @param id ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin cập nhật
   * @returns Thông tin cập nhật agent thành công
   */
  @Transactional()
  async updateAgent(
    id: string,
    userId: number,
    updateDto: UpdateAgentDto,
  ): Promise<UpdateAgentResponseDto> {
    try {
      // Lấy thông tin agent
      // Lấy thông tin agent từ repository
      const agent = await this.agentRepository.findOne({ where: { id } });
      if (!agent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Lấy thông tin agent user
      const agentUser = await this.agentUserRepository.findOne({
        where: { id, userId },
      });
      if (!agentUser) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Cập nhật thông tin agent
      if (updateDto.name) {
        agent.name = updateDto.name;
      }
      if (updateDto.modelConfig) {
        // Kiểm tra model và provider_id
        if (typeof updateDto.modelConfig === 'object') {
          const modelConfigObj = updateDto.modelConfig;

          // Loại bỏ trường providerName nếu client gửi lên
          if ('providerName' in modelConfigObj) {
            const { providerName, ...validConfig } = modelConfigObj as any;
            updateDto.modelConfig = validConfig;
          }

          // Kiểm tra model dựa trên provider_id
          try {
            await this.validateModel(
              modelConfigObj.modelId,
              modelConfigObj.provider_id
            );
          } catch (error) {
            if (error instanceof AppException) {
              throw error;
            }
            throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED, error.message);
          }

          // Tạo ModelConfig từ updateDto.modelConfig
          const modelConfig: ModelConfig = {
            modelId: modelConfigObj.modelId,
            provider_id: modelConfigObj.provider_id || null,
            temperature: modelConfigObj.temperature,
            top_p: modelConfigObj.top_p,
            top_k: modelConfigObj.top_k,
            max_tokens: modelConfigObj.max_tokens,
          };

          agent.modelConfig = modelConfig;
        } else {
          agent.modelConfig = updateDto.modelConfig;
        }
      }
      if (updateDto.instruction !== undefined) {
        agent.instruction = updateDto.instruction;
      }
      agent.updatedAt = Date.now();

      // Lưu agent
      await this.agentRepository.save(agent);

      // Cập nhật thông tin agent user
      if (updateDto.profile) {
        // Chuyển đổi từ ProfileDto sang ProfileAgent
        const profileAgent = {
          ...agentUser.profile,
          gender: updateDto.profile.gender ? updateDto.profile.gender as any : undefined,
          dateOfBirth: updateDto.profile.dateOfBirth ? new Date(updateDto.profile.dateOfBirth) : undefined,
          position: updateDto.profile.position,
          // Chỉ sử dụng các trường có trong ProfileDto
          education: updateDto.profile.education,
          skills: updateDto.profile.skills,
          personality: updateDto.profile.personality,
          languages: updateDto.profile.languages,
          nations: updateDto.profile.nations,
        } as any; // Sử dụng type assertion để tránh lỗi TypeScript

        agentUser.profile = profileAgent;
        await this.agentUserRepository.save(agentUser);
      }

      // Tạo S3 key cho avatar mới nếu có
      let avatarUploadUrl = '';
      if (updateDto.avatarMimeType) {
        if (!userId || isNaN(userId)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_S3_KEY, 'ID người dùng không hợp lệ');
        }

        const avatarKey = generateS3Key({
          baseFolder: userId.toString(),
          categoryFolder: CategoryFolderEnum.AGENT,
        });

        // Cập nhật avatar key cho agent
        agent.avatar = avatarKey;
        await this.agentRepository.save(agent);

        // Tạo URL để upload avatar
        avatarUploadUrl = await this.s3Service.getDownloadUrl(
          avatarKey,
          TimeIntervalEnum.ONE_HOUR
        );
      }

      // Xử lý tài nguyên media nếu có
      if (updateDto.mediaIds && updateDto.mediaIds.length > 0) {
        // Kiểm tra tất cả media có tồn tại không
        const existingMedias = await this.mediaRepository.find({
          where: { id: In(updateDto.mediaIds) }
        });
        const existingMediaIds = existingMedias.map(media => media.id);
        const nonExistingMediaIds = updateDto.mediaIds.filter(id => !existingMediaIds.includes(id));

        if (nonExistingMediaIds.length > 0) {
          throw new AppException(
            AGENT_ERROR_CODES.MEDIA_NOT_FOUND,
            `Không tìm thấy media với ID: ${nonExistingMediaIds.join(', ')}`
          );
        }

        // Kiểm tra quyền truy cập media
        const unauthorizedMedias = existingMedias.filter(media => media.ownedBy !== userId);
        if (unauthorizedMedias.length > 0) {
          throw new AppException(
            AGENT_ERROR_CODES.MEDIA_NOT_FOUND,
            `Không có quyền truy cập media với ID: ${unauthorizedMedias.map(m => m.id).join(', ')}`
          );
        }

        // Thêm từng media vào agent
        for (const mediaId of updateDto.mediaIds) {
          await this.agentMediaRepository.addMedia(agent.id, mediaId);
        }
      }

      // Xử lý tài nguyên product nếu có
      if (updateDto.productIds && updateDto.productIds.length > 0) {
        // Chuyển đổi productIds từ string sang number
        const productIdsNumber = updateDto.productIds.map(id => parseInt(id));

        // Kiểm tra tất cả product có tồn tại không
        const existingProducts = await this.userProductRepository.find({
          where: { id: In(productIdsNumber) }
        });

        const existingProductIds = existingProducts.map(product => product.id.toString());
        const nonExistingProductIds = updateDto.productIds.filter(id => !existingProductIds.includes(id));

        if (nonExistingProductIds.length > 0) {
          throw new AppException(
            AGENT_ERROR_CODES.PRODUCT_NOT_FOUND,
            `Không tìm thấy sản phẩm với ID: ${nonExistingProductIds.join(', ')}`
          );
        }

        // Kiểm tra quyền truy cập product
        const unauthorizedProducts = existingProducts.filter(product => product.createdBy !== userId);
        if (unauthorizedProducts.length > 0) {
          throw new AppException(
            AGENT_ERROR_CODES.PRODUCT_NOT_FOUND,
            `Không có quyền truy cập sản phẩm với ID: ${unauthorizedProducts.map(p => p.id).join(', ')}`
          );
        }

        // Thêm từng product vào agent
        for (const productId of updateDto.productIds) {
          await this.agentProductRepository.addProduct(agent.id, productId);
        }
      }

      // Xử lý tài nguyên url nếu có
      if (updateDto.urlIds && updateDto.urlIds.length > 0) {
        // Kiểm tra tất cả URL có tồn tại không
        const existingUrls = await this.urlRepository.find({
          where: { id: In(updateDto.urlIds) }
        });
        const existingUrlIds = existingUrls.map(url => url.id);
        const nonExistingUrlIds = updateDto.urlIds.filter(id => !existingUrlIds.includes(id));

        if (nonExistingUrlIds.length > 0) {
          throw new AppException(
            AGENT_ERROR_CODES.URL_NOT_FOUND,
            `Không tìm thấy URL với ID: ${nonExistingUrlIds.join(', ')}`
          );
        }

        // Kiểm tra quyền truy cập URL
        const unauthorizedUrls = existingUrls.filter(url => url.ownedBy !== userId);
        if (unauthorizedUrls.length > 0) {
          throw new AppException(
            AGENT_ERROR_CODES.URL_NOT_FOUND,
            `Không có quyền truy cập URL với ID: ${unauthorizedUrls.map(u => u.id).join(', ')}`
          );
        }

        // Thêm từng URL vào agent
        for (const urlId of updateDto.urlIds) {
          await this.agentUrlRepository.addUrl(agent.id, urlId);
        }
      }

      // Cập nhật thông tin vector store nếu có
      if (updateDto.vectorStoreId !== undefined) {
        // Ở đây có thể thêm logic kiểm tra vectorStoreId có tồn tại không
        // Ví dụ: gọi API hoặc kiểm tra trong database

        // Giả sử chúng ta có một danh sách các vector store ID hợp lệ
        // const validVectorStoreIds = ['vector-store-1', 'vector-store-2'];
        // if (updateDto.vectorStoreId && !validVectorStoreIds.includes(updateDto.vectorStoreId)) {
        //   throw new AppException(AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND, 'Vector store không tồn tại');
        // }

        agent.vectorStoreId = updateDto.vectorStoreId;
        await this.agentRepository.save(agent);
      }

      return {
        id: agent.id,
        avatarUploadUrl,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Xóa agent
   * @param id ID của agent
   * @param userId ID của người dùng
   */
  @Transactional()
  async deleteAgent(id: string, userId: number): Promise<void> {
    try {
      // Sử dụng repository để xóa mềm agent
      await this.agentUserRepository.softDeleteAgent(id, userId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED, error.message);
    }
  }

  /**
   * Đảo ngược trạng thái hoạt động của agent
   * @param id ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin cập nhật trạng thái thành công
   */
  @Transactional()
  async updateAgentActive(
    id: string,
    userId: number,
  ): Promise<{ id: string; active: boolean }> {
    try {
      // Kiểm tra agent có tồn tại không
      const result = await this.agentUserRepository.findAgentByIdAndUserId(id, userId);
      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Lấy trạng thái hiện tại và đảo ngược nó
      const currentActive = result.agentUser.active || false;
      const newActive = !currentActive;

      // Sử dụng repository để cập nhật trạng thái hoạt động
      await this.agentUserRepository.updateAgentActive(id, userId, newActive);

      return {
        id,
        active: newActive,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật trạng thái hoạt động của agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật loại agent cho một agent
   * @param id ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin cập nhật loại agent
   */
  @Transactional()
  async updateAgentType(
    id: string,
    userId: number,
    updateDto: UpdateAgentTypeDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại không
      const result = await this.agentUserRepository.findAgentByIdAndUserId(id, userId);
      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Kiểm tra loại agent có tồn tại không
      const typeAgent = await this.typeAgentRepository.findById(updateDto.typeId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Kiểm tra quyền truy cập loại agent
      if (typeAgent.userId && typeAgent.userId !== userId && typeAgent.status !== 'APPROVED') {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Sử dụng repository để cập nhật loại agent
      await this.agentUserRepository.updateAgentType(id, userId, updateDto.typeId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật loại agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật kho vector cho một agent
   * @param id ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin cập nhật kho vector
   */
  @Transactional()
  async updateAgentVectorStore(
    id: string,
    userId: number,
    updateDto: UpdateAgentVectorStoreDto,
  ): Promise<void> {
    try {
      // Lấy thông tin agent - chỉ select các trường cần thiết
      const agent = await this.agentRepository.createBaseQuery()
        .select(['agent.id'])
        .where('agent.id = :id', { id })
        .andWhere('agent.deletedAt IS NULL')
        .getOne();
      if (!agent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Lấy thông tin agent user - chỉ select các trường cần thiết
      const agentUser = await this.agentUserRepository.createBaseQuery()
        .select(['agentUser.id', 'agentUser.userId'])
        .where('agentUser.id = :id', { id })
        .andWhere('agentUser.userId = :userId', { userId })
        .getOne();
      if (!agentUser) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Kiểm tra vector store có tồn tại không
      const vectorStore = await this.vectorStoreRepository.findOne({
        where: { id: updateDto.vector_store_id }
      });

      if (!vectorStore) {
        throw new AppException(
          AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
          `Vector store với ID ${updateDto.vector_store_id} không tồn tại`
        );
      }

      // Cập nhật vector store cho agent
      agent.vectorStoreId = updateDto.vector_store_id;
      await this.agentRepository.save(agent);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật kho vector: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Lấy thống kê agent
   * @param id ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Thống kê agent
   */
  async getAgentStatistics(
    id: string,
    userId: number,
    _queryDto: AgentStatisticsQueryDto, // Thêm dấu gạch dưới để đánh dấu tham số không sử dụng
  ): Promise<AgentStatisticsResponseDto> {
    try {
      // Lấy thông tin agent
      const agent = await this.agentRepository.findOne({ where: { id } });
      if (!agent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Lấy thông tin agent user
      const agentUser = await this.agentUserRepository.findOne({
        where: { id, userId },
      });
      if (!agentUser) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // TODO: Implement lấy thống kê agent
      // Đây là dữ liệu mẫu, cần thay thế bằng dữ liệu thực tế
      return {
        totalConversations: 120,
        totalMessages: 1850,
        averageMessagesPerConversation: 15.4,
        averageResponseTime: 1.2,
        dailyStats: [
          {
            date: 1672531200000,
            conversations: 10,
            messages: 150,
          },
          {
            date: 1672617600000,
            conversations: 15,
            messages: 220,
          },
        ],
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy thống kê agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_STATISTICS_FAILED, error.message);
    }
  }



  /**
   * Kiểm tra model có tồn tại không và có trạng thái APPROVED không
   * @param modelId ID của model
   * @param provider_id ID của provider (nếu là model của user)
   */
  private async validateModel(
    modelId: string,
    provider_id?: string,
  ): Promise<{ type?: string }> {
    try {
      if (!provider_id) {
        // Kiểm tra model hệ thống
        const model = await this.baseModelAdminService.getModelById(modelId);;

        if (!model) {
          throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
        }

        // Kiểm tra model có trạng thái APPROVED không
        if (model.status !== 'APPROVED') {
          throw new AppException(
            AGENT_ERROR_CODES.MODEL_NOT_APPROVED,
            `Model với ID ${modelId} chưa được phê duyệt, không thể sử dụng`
          );
        }

        // Trả về thông tin type của model
        return { type: 'system' };
      } else {
        // Kiểm tra model của user
        try {
          // Lấy thông tin provider từ provider_id
          // Sử dụng repository để lấy thông tin provider
          const provider = await this.userProviderModelRepository.findOne({
            where: { id: provider_id }
          });

          // Kiểm tra provider có bị xóa không
          if (provider?.deletedAt) {
            throw new AppException(
              AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED,
              `Provider với ID ${provider_id} đã bị xóa`
            );
          }

          if (!provider) {
            throw new AppException(
              AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED,
              `Provider với ID ${provider_id} không tồn tại`
            );
          }

          // Giải mã API key
          const apiKeyDecode = this.apiKeyEncryptionHelper.decryptUserApiKey(provider.apiKey, provider.userId);

          // Xác định loại provider và kiểm tra model
          const providerType = provider.type;

          // Sử dụng helper để kiểm tra model
          await this.aiProviderHelper.retrieveModel(modelId, providerType, provider.apiKey, false, provider.userId);

          // Nếu không có lỗi, model tồn tại
          return { type: providerType };
        } catch (error) {
          if (error instanceof AppException) {
            throw error;
          }
          this.logger.error(`Lỗi khi kiểm tra model: ${error.message}`, error.stack);
          throw new AppException(
            AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED,
            `Model với ID ${modelId} không tồn tại với provider ${provider_id}: ${error.message}`
          );
        }
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error validating model: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
    }
  }

  /**
   * Kiểm tra cấu hình của type agent
   * @param typeId ID của type agent
   */
  private async validateTypeAgentConfig(typeId: number): Promise<{
    hasProfile: boolean;
    hasConversion: boolean;
    hasResources: boolean;
  }> {
    try {
      // Lấy thông tin type agent
      const typeAgent = await this.typeAgentRepository.findById(typeId);

      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Lấy cấu hình của type agent
      const config = typeAgent.config || {};

      return {
        hasProfile: config.hasProfile || false,
        hasConversion: config.hasConversion || false,
        hasResources: config.hasResources || false,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi kiểm tra cấu hình type agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param agent Entity Agent
   * @param agentUser Entity AgentUser
   * @returns AgentListItemDto
   */
  private async mapToAgentListItemDto(agent: Agent, agentUser: AgentUser): Promise<AgentListItemDto> {
    // Tìm rank phù hợp dựa trên exp
    const exp = agentUser.exp || 0;
    const ranks = await this.agentRankRepository.findAll();
    const rank = ranks.find(r => exp >= r.minExp && exp < r.maxExp) || ranks[0];

    // Sử dụng AgentMapper để chuyển đổi sang DTO
    return AgentMapper.toListItemDto(
      {
        id: agent.id,
        name: agent.name,
        avatar: agent.avatar,
        typeId: agentUser.typeId,
        typeName: agentUser.typeAgent?.name || '',
        active: agentUser.active || false,
        createdAt: agent.createdAt,
        updatedAt: agent.updatedAt,
        exp: agentUser.exp || 0,
        modelConfig: agent.modelConfig,
      },
      this.cdnService,
      rank
    );
  }

  /**
   * Chuyển đổi từ entity sang DTO chi tiết
   * @param agent Entity Agent
   * @param agentUser Entity AgentUser
   * @returns AgentDetailDto
   */
  private async mapToAgentDetailDto(agent: Agent, agentUser: AgentUser): Promise<AgentDetailDto> {
    // Chuyển đổi ModelConfig sang ModelConfigDto
    const modelConfigDto = {
      modelId: agent.modelConfig?.modelId,
      provider_id: agent.modelConfig?.provider_id || null,
      temperature: agent.modelConfig?.temperature || 0.7,
      top_p: agent.modelConfig?.top_p || 0.9,
      top_k: agent.modelConfig?.top_k || 40,
      max_tokens: agent.modelConfig?.max_tokens || 1000,
    } as ModelConfigDto; // Sử dụng type assertion để tránh lỗi TypeScript

    // Xác định provider_type từ modelId
    const provider_type = await this.validateModel(agent.modelConfig?.modelId);

    // Chuyển đổi ProfileAgent sang ProfileDto
    const profileDto = {
      gender: agentUser.profile?.gender?.toString() || '',
      dateOfBirth: agentUser.profile?.dateOfBirth instanceof Date
        ? agentUser.profile.dateOfBirth.getTime()
        : (typeof agentUser.profile?.dateOfBirth === 'string'
          ? new Date(agentUser.profile.dateOfBirth).getTime()
          : 946684800000), // Mặc định 01/01/2000
      position: agentUser.profile?.position || '',
      education: agentUser.profile?.education || '',
      skills: agentUser.profile?.skills || [],
      personality: agentUser.profile?.personality || [],
      languages: agentUser.profile?.languages || [],
      nations: agentUser.profile?.nations || '',
    } as ProfileDto; // Sử dụng type assertion để tránh lỗi TypeScript

    // Tạo URL CDN cho avatar nếu có
    let avatarUrl = agent.avatar;
    if (agent.avatar) {
      try {
        avatarUrl = this.cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_DAY);
      } catch (error) {
        this.logger.warn(`Không thể tạo URL CDN cho avatar: ${error.message}`);
      }
    }

    // Lấy tên của loại agent nếu có
    let typeName = '';
    if (agentUser.typeAgent && agentUser.typeAgent.name) {
      typeName = agentUser.typeAgent.name;
    }

    // Tạo vector store
    const vectorStores = agent.vectorStoreId ? {
      id: agent.vectorStoreId,
      name: agent.vectorStoreId // Using ID as name since we don't have the actual name
    } : undefined;

    // Tìm rank phù hợp dựa trên exp
    const exp = agentUser.exp || 0;
    const ranks = await this.agentRankRepository.findAll();
    const rank = ranks.find(r => exp >= r.minExp && exp < r.maxExp) || ranks[0];

    // Tạo URL CDN cho badge nếu có
    let badgeUrl = '';
    if (rank && rank.badge) {
      try {
        badgeUrl = this.cdnService.generateUrlView(rank.badge, TimeIntervalEnum.ONE_DAY) || '';
      } catch (error) {
        this.logger.warn(`Không thể tạo URL CDN cho badge: ${error.message}`);
      }
    }

    return {
      id: agent.id,
      name: agent.name,
      avatar: avatarUrl,
      typeId: agentUser.typeId,
      typeName: typeName,
      modelConfig: modelConfigDto,
      provider_type: TypeProviderUtil.getMimeType(provider_type.type || ''),
      instruction: agent.instruction || '',
      profile: profileDto,
      active: agentUser.active || false,
      vectorStores: vectorStores,
      createdAt: agent.createdAt,
      updatedAt: agent.updatedAt,
      // Thêm các trường mới
      exp: agentUser.exp || 0,
      expMax: rank ? rank.maxExp : 100,
      level: rank ? rank.id : 1,
      badge_url: badgeUrl,
      model_id: agent.modelConfig?.modelId || '',
    };
  }
}
