import { Controller, Post, Get, Patch, Delete, Body, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import { ErrorCode } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { INTEGRATION_ERROR_CODES } from '@modules/integration/exceptions';
import { AgentFacebookPageService } from '../services/agent-facebook-page.service';
import {
  IntegrateFacebookPageDto,
  AgentFacebookPageListDto
} from '../dto/facebook-page';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các API liên quan đến tích hợp Facebook Page với Agent
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents/:agentId/facebook-pages')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AgentFacebookPageController {
  constructor(private readonly agentFacebookPageService: AgentFacebookPageService) {}

  /**
   * API tích hợp Facebook Page vào Agent
   * @param agentId ID của Agent
   * @param dto Thông tin Facebook Page cần tích hợp
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo tích hợp thành công
   */
  @Post()
  @ApiOperation({ summary: 'Tích hợp Facebook Page vào Agent' })
  @ApiParam({
    name: 'agentId',
    description: 'ID của Agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: 200,
    description: 'Tích hợp Facebook Page thành công',
    schema: ApiResponseDto.getSchema('Tích hợp Facebook Page thành công')
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_ALREADY_INTEGRATED,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_SUBSCRIBE_FAILED,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_INTEGRATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async integrateFacebookPage(
    @Param('agentId') agentId: string,
    @Body() dto: IntegrateFacebookPageDto,
    @CurrentUser('id') userId: number
  ) {
    await this.agentFacebookPageService.integrateFacebookPage(agentId, userId, dto);
    return ApiResponseDto.success('Tích hợp Facebook Page thành công');
  }

  /**
   * API lấy danh sách Facebook Page trong Agent
   * @param agentId ID của Agent
   * @param userId ID của người dùng hiện tại
   * @returns Danh sách Facebook Page
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách Facebook Page trong Agent' })
  @ApiParam({
    name: 'agentId',
    description: 'ID của Agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách Facebook Page thành công',
    type: AgentFacebookPageListDto
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_LIST_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getFacebookPages(
    @Param('agentId') agentId: string,
    @CurrentUser('id') userId: number
  ) {
    const result = await this.agentFacebookPageService.getFacebookPages(agentId, userId);
    return ApiResponseDto.success(result);
  }

  /**
   * API bật/tắt trạng thái active của Facebook Page (toggle)
   * @param agentId ID của Agent
   * @param pageId UUID của Facebook Page trong hệ thống
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo cập nhật thành công
   */
  @Patch(':pageId/toggle-active')
  @ApiOperation({ summary: 'Bật/tắt trạng thái active của Facebook Page' })
  @ApiParam({
    name: 'agentId',
    description: 'ID của Agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiParam({
    name: 'pageId',
    description: 'UUID của Facebook Page trong hệ thống',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái Facebook Page thành công',
    schema: ApiResponseDto.getSchema('Cập nhật trạng thái Facebook Page thành công')
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_INTEGRATED,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async toggleFacebookPageActive(
    @Param('agentId') agentId: string,
    @Param('pageId') pageId: string,
    @CurrentUser('id') userId: number
  ) {
    await this.agentFacebookPageService.toggleFacebookPageActive(agentId, pageId, userId);
    return ApiResponseDto.success('Cập nhật trạng thái Facebook Page thành công');
  }

  /**
   * API gỡ Facebook Page khỏi Agent
   * @param agentId ID của Agent
   * @param pageId UUID của Facebook Page trong hệ thống
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo gỡ bỏ thành công
   */
  @Delete(':pageId')
  @ApiOperation({ summary: 'Gỡ Facebook Page khỏi Agent' })
  @ApiParam({
    name: 'agentId',
    description: 'ID của Agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiParam({
    name: 'pageId',
    description: 'UUID của Facebook Page trong hệ thống',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: 200,
    description: 'Gỡ Facebook Page thành công',
    schema: ApiResponseDto.getSchema('Gỡ Facebook Page thành công')
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_INTEGRATED,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_REMOVE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async removeFacebookPage(
    @Param('agentId') agentId: string,
    @Param('pageId') pageId: string,
    @CurrentUser('id') userId: number
  ) {
    await this.agentFacebookPageService.removeFacebookPage(agentId, pageId, userId);
    return ApiResponseDto.success('Gỡ Facebook Page thành công');
  }
}
