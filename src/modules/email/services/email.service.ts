import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom, catchError } from 'rxjs';
import { AxiosError } from 'axios';
import { SendEmailDto } from '../interface/send-email.dto';
import { AppException, ErrorCode } from '@/common';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly emailApiBaseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    // Lấy URL hoàn toàn từ biến môi trường
    const apiUrlFromEnv = this.configService.get<string>('EXTERNAL_EMAIL_API_URL');

    // Kiểm tra xem biến môi trường đã được định nghĩa chưa
    if (!apiUrlFromEnv) {
      this.logger.error('Environment variable EXTERNAL_EMAIL_API_URL is not defined.');
      throw new AppException(ErrorCode.EMAIL_SENDING_ERROR, 'External Email API URL is not configured in environment variables.');
    }

    this.emailApiBaseUrl = apiUrlFromEnv;
    this.logger.log(`Email API Base URL configured: ${this.emailApiBaseUrl}`); // Cập nhật log
  }

  /**
   * Gửi email thông qua API.
   * @param sendEmailDto - Dữ liệu email (to, subject, body).
   * @returns Promise chứa kết quả từ API hoặc ném lỗi nếu thất bại.
   */
  async sendEmail(sendEmailDto: SendEmailDto): Promise<any> {
    const apiUrl = `${this.emailApiBaseUrl}/a_email/send-email`;
    this.logger.log(`Sending email via API: ${apiUrl} to ${sendEmailDto.to}`);

    try {
      const response = await firstValueFrom(
        this.httpService.post(apiUrl, sendEmailDto, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': '*/*',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(
              `Error sending email to ${sendEmailDto.to}: ${error.response?.status} ${error.response?.data || error.message}`,
              error.stack,
            );
            throw new AppException(ErrorCode.EMAIL_SENDING_ERROR, `Failed to send email via API: ${error.message}`);
          }),
        ),
      );

      this.logger.log(`Successfully sent email to ${sendEmailDto.to}. API Response Status: ${response.status}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
}