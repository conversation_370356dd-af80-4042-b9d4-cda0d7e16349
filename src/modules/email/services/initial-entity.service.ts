import { Injectable, Logger } from '@nestjs/common';
import { PlaceholdersEnum } from '../interface/placeholders.enum';
import { TemplateEntityData } from '../interface/template-entity.interface';
import { EntityProviderData } from '../dto/send-email.dto';
import { TwoFA } from '../interface/two-fa.class';
import { UserService } from '@/modules/user/user/service';

/**
 * Service khởi tạo entity cho template
 * Chuyển đổi từ InitialEntity.java
 */
@Injectable()
export class InitialEntityService {
  private readonly logger = new Logger(InitialEntityService.name);

  constructor(private readonly userService: UserService) {}

  /**
   * Khởi tạo template entity từ provider và danh sách placeholders
   * @param provider Provider dữ liệu entity
   * @param keys Danh sách placeholders cần sử dụng
   * @returns TemplateEntityData đã được khởi tạo
   */
  async initialTemplateEntity(
    provider: EntityProviderData,
    keys: PlaceholdersEnum[],
  ): Promise<TemplateEntityData> {
    this.logger.log(`Khởi tạo template entity với ${keys.length} placeholders`);

    const data: TemplateEntityData = {};

    // Xử lý UserEntity
    if (
      provider.userId !== undefined &&
      this.hasAnyPlaceholderOfType(keys, 'UserEntity')
    ) {
      try {
        const user = await this.userService.findOne(provider.userId);

        if (user) {
          data.user = user;
          this.logger.log(`Đã tìm thấy và thêm user với ID: ${user.id}`);
        } else {
          this.logger.warn(`Không tìm thấy user với ID: ${provider.userId}`);
        }
      } catch (error) {
        this.logger.error(
          `Lỗi khi tìm user với ID ${provider.userId}: ${error.message}`,
        );
      }
    }

    // Xử lý mã xác thực 2FA
    if (
      provider.twoFACode !== undefined &&
      this.hasAnyPlaceholderOfType(keys, 'TwoFA')
    ) {
      data.twoFA = new TwoFA(provider.twoFACode);
      this.logger.log('Đã thêm dữ liệu TwoFA');
    }

    // Xử lý mật khẩu mới
    if (
      provider.newPassword !== undefined &&
      this.hasAnyPlaceholderOfType(keys, 'NewPassword')
    ) {
      data.newPassword = { password: provider.newPassword };
      this.logger.log('Đã thêm dữ liệu NewPassword');
    }

    return data;
  }

  /**
   * Kiểm tra xem danh sách placeholder có chứa bất kỳ placeholder nào của loại entity không
   * @param keys Danh sách placeholder cần kiểm tra
   * @param entityType Tên loại entity
   * @returns true nếu có ít nhất một placeholder thuộc loại entity, ngược lại false
   */
  private hasAnyPlaceholderOfType(
    keys: PlaceholdersEnum[],
    entityType: string,
  ): boolean {
    const entityPlaceholders = this.getTypeKey(entityType);
    return keys.some((key) => entityPlaceholders.includes(key));
  }

  /**
   * Lấy danh sách placeholder theo loại entity
   * @param entityClass Loại entity
   * @returns Mảng các PlaceholdersEnum tương ứng với loại entity
   */
  private getTypeKey(entityClass: string): PlaceholdersEnum[] {
    switch (entityClass) {
      case 'UserEntity':
        return [
          PlaceholdersEnum.NAME,
          PlaceholdersEnum.PHONE,
          PlaceholdersEnum.ADDRESS,
          PlaceholdersEnum.USER_ID,
          PlaceholdersEnum.USER_NAME,
          PlaceholdersEnum.USER_EMAIL,
          PlaceholdersEnum.USER_PHONE,
          PlaceholdersEnum.USER_ADDRESS,
          PlaceholdersEnum.USER_POINT,
          PlaceholdersEnum.USER_TYPE,
          PlaceholdersEnum.USER_IS_VERIFY,
          PlaceholdersEnum.USER_GENDER,
          PlaceholdersEnum.USER_DATE_OF_BIRTH,
          PlaceholdersEnum.USER_NATION,
          PlaceholdersEnum.USER_FILE_SLOT_REMAINING,
          PlaceholdersEnum.USER_STORAGE_REMAINING,
          PlaceholdersEnum.USER_AVATAR,
          PlaceholdersEnum.USER_CREATE_DATE,
          PlaceholdersEnum.USER_IS_ENABLE,
          PlaceholdersEnum.USER_TOTAL_DEPOSITE,
          PlaceholdersEnum.USER_PLATFORM,
          PlaceholdersEnum.USER_IS_PAID,
          PlaceholdersEnum.USER_AFFILIATE_ID,
          PlaceholdersEnum.USER_REASON_DISABLE,
        ];
      case 'TwoFA':
        return [PlaceholdersEnum.TWO_FA_CODE];
      case 'NewPassword':
        return [PlaceholdersEnum.NEW_PASSWORD];
      default:
        return [];
    }
  }
}
