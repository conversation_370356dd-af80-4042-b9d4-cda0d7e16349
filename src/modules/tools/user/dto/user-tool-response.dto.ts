import { ApiProperty } from '@nestjs/swagger';
import { ToolStatusEnum } from '../../constants/tool-status.enum';

/**
 * DTO đơn giản cho thông tin phiên bản tool của người dùng
 * Chỉ chứa các trường cơ bản: id, versionName, toolName
 */
export class UserToolVersionSimpleDto {
  @ApiProperty({
    description: 'ID của phiên bản',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Tên phiên bản',
    example: 'v1.0.0',
  })
  versionName: string;

  @ApiProperty({
    description: 'Số phiên bản (deprecated)',
    example: 1,
    required: false,
  })
  versionNumber?: number;

  @ApiProperty({
    description: 'Tên tool trong định nghĩa code',
    example: 'searchTool',
  })
  toolName: string;
}

/**
 * DTO cho thông tin phiên bản tool của người dùng
 */
export class UserToolVersionDto {
  @ApiProperty({
    description: 'ID của phiên bản',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Tên phiên bản',
    example: 'v1.0.0',
  })
  versionName: string;

  @ApiProperty({
    description: 'Số phiên bản (deprecated)',
    example: 1,
    required: false,
  })
  versionNumber?: number;

  @ApiProperty({
    description: 'Tên tool trong định nghĩa code',
    example: 'searchTool',
  })
  toolName: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về chức năng của tool',
    example: 'Tìm kiếm thông tin từ nhiều nguồn dữ liệu khác nhau',
    nullable: true,
  })
  toolDescription: string | null;

  @ApiProperty({
    description: 'Tham số của tool',
    example: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Câu truy vấn tìm kiếm',
        },
      },
      required: ['query'],
    },
  })
  parameters: Record<string, any>;

  @ApiProperty({
    description: 'Mô tả những thay đổi so với phiên bản trước',
    example: 'Thêm tham số lọc theo ngày',
    nullable: true,
  })
  changeDescription: string | null;

  @ApiProperty({
    description: 'Trạng thái của phiên bản',
    enum: ToolStatusEnum,
    example: ToolStatusEnum.APPROVED,
  })
  status: ToolStatusEnum;

  @ApiProperty({
    description: 'Thời điểm tạo phiên bản (unix timestamp)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Đã chỉnh sửa',
    example: true,
  })
  edited: boolean;
}

/**
 * DTO cho thông tin tool của người dùng trong danh sách
 */
export class UserToolListItemDto {
  @ApiProperty({
    description: 'ID của tool',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Tên của tool',
    example: 'Công cụ tìm kiếm',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả về tool',
    example: 'Công cụ giúp tìm kiếm thông tin từ nhiều nguồn',
    nullable: true,
  })
  description: string | null;

  @ApiProperty({
    description: 'Danh sách nhóm tool mà tool này thuộc về',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        name: { type: 'string', example: 'Nhóm công cụ tìm kiếm' }
      }
    },
    example: [{ id: 1, name: 'Nhóm công cụ tìm kiếm' }]
  })
  groups: { id: number; name: string }[];

  @ApiProperty({
    description: 'Thời điểm tạo tool (unix timestamp)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật tool (unix timestamp)',
    example: 1625097600000,
  })
  updatedAt: number;

  @ApiProperty({
    description: 'ID của tool gốc từ admin',
    example: '550e8400-e29b-41d4-a716-446655440000',
    nullable: true,
  })
  originalId: string | null;

  @ApiProperty({
    description: 'Có bản cập nhật mới',
    example: false,
  })
  hasUpdate: boolean;

  @ApiProperty({
    description: 'Trạng thái kích hoạt của tool',
    example: true,
  })
  active: boolean;
}

/**
 * DTO cho thông tin chi tiết tool của người dùng
 */
export class UserToolDetailDto extends UserToolListItemDto {
  @ApiProperty({
    description: 'Phiên bản mặc định của tool',
    type: UserToolVersionDto,
    nullable: true,
  })
  defaultVersion: UserToolVersionDto | null;

  @ApiProperty({
    description: 'Danh sách các phiên bản của tool (chỉ bao gồm id, versionName và toolName)',
    type: [UserToolVersionSimpleDto],
  })
  versions: UserToolVersionSimpleDto[];
}
