import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { UserTool, UserToolVersion } from '../../entities';
import {
  UserToolRepository,
  UserToolVersionRepository,
  AdminToolRepository,
  AdminToolVersionRepository,
  UserGroupToolMappingRepository,
} from '../../repositories';
import {
  CloneAdminToolDto,
  CloneAllPublicToolsDto,
  EditToolVersionDto,
  QueryUserToolDto,
  RollbackToAdminVersionDto,
  UpdateFromAdminDto,
  UserToolDetailDto,
  UserToolListItemDto,
} from '../dto';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { ToolStatusEnum } from '../../constants/tool-status.enum';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class UserToolService {
  private readonly logger = new Logger(UserToolService.name);

  constructor(
    private readonly userToolRepository: UserToolRepository,
    private readonly userToolVersionRepository: UserToolVersionRepository,
    private readonly adminToolRepository: AdminToolRepository,
    private readonly adminToolVersionRepository: AdminToolVersionRepository,
    private readonly userGroupToolMappingRepository: UserGroupToolMappingRepository,
  ) {}

  /**
   * Lấy danh sách tool của người dùng với phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tool với phân trang
   */
  async getUserTools(
    userId: number,
    queryDto: QueryUserToolDto,
  ): Promise<PaginatedResult<UserToolListItemDto>> {
    try {
      const { page, limit, search, hasUpdate, sortBy, sortDirection } = queryDto;

      // Lấy danh sách tool với thông tin group từ admin tool
      const result = await this.userToolRepository.findToolsWithGroups(
        page,
        limit,
        userId,
        search,
        ToolStatusEnum.APPROVED, // Chỉ lấy tool có trạng thái APPROVED
        hasUpdate,
        sortBy,
        sortDirection,
      );

      // Group các tool theo ID và tập hợp groups
      const toolsMap = new Map<string, any>();

      result.items.forEach((item: any) => {
        if (!toolsMap.has(item.tool_id)) {
          toolsMap.set(item.tool_id, {
            id: item.tool_id,
            name: item.tool_name,
            description: item.tool_description,
            createdAt: item.tool_createdat,
            updatedAt: item.tool_updatedat,
            originalId: item.tool_originalid,
            hasUpdate: item.tool_hasupdate,
            active: item.tool_active,
            groups: []
          });
        }

        // Thêm group nếu có
        if (item.grouptool_id && item.grouptool_name) {
          const tool = toolsMap.get(item.tool_id);
          const existingGroup = tool.groups.find((g: any) => g.id === item.grouptool_id);
          if (!existingGroup) {
            tool.groups.push({
              id: item.grouptool_id,
              name: item.grouptool_name
            });
          }
        }
      });

      // Chuyển đổi sang DTO
      const items = Array.from(toolsMap.values()).map((tool) => {
        const dto = new UserToolListItemDto();
        dto.id = tool.id;
        dto.name = tool.name;
        dto.description = tool.description;
        dto.createdAt = tool.createdAt;
        dto.updatedAt = tool.updatedAt;
        dto.originalId = tool.originalId;
        dto.hasUpdate = tool.hasUpdate;
        dto.active = tool.active;
        dto.groups = tool.groups;

        return dto;
      });

      return {
        items,
        meta: {
          totalItems: result.total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(result.total / limit),
          currentPage: page
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get user tools: ${error.message}`, error.stack);
      throw new AppException(TOOLS_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }

  /**
   * Lấy thông tin chi tiết tool của người dùng
   * @param toolId ID của tool
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết tool
   */
  async getUserToolById(toolId: string, userId: number): Promise<UserToolDetailDto> {
    try {
      // Lấy thông tin tool với groups từ admin tool
      const toolWithGroups = await this.userToolRepository.findToolByIdWithGroups(toolId, userId);
      if (!toolWithGroups || toolWithGroups.length === 0) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Lấy thông tin tool đầu tiên (vì có thể có nhiều rows do join với groups)
      const toolInfo = toolWithGroups[0];

      // Tập hợp tất cả groups
      const groups: { id: number; name: string }[] = [];
      toolWithGroups.forEach((item: any) => {
        if (item.grouptool_id && item.grouptool_name) {
          const existingGroup = groups.find(g => g.id === item.grouptool_id);
          if (!existingGroup) {
            groups.push({
              id: item.grouptool_id,
              name: item.grouptool_name
            });
          }
        }
      });

      // Lấy tất cả phiên bản của tool
      const versions = await this.userToolVersionRepository.findVersionsByToolIdAndUserId(toolId, userId);

      // Lấy phiên bản mặc định (phiên bản mới nhất)
      const defaultVersion = versions.length > 0 ? versions[0] : null;

      // Chuyển đổi sang DTO
      const dto = new UserToolDetailDto();
      dto.id = toolInfo.tool_id;
      dto.name = toolInfo.tool_name;
      dto.description = toolInfo.tool_description;
      dto.createdAt = toolInfo.tool_createdat;
      dto.updatedAt = toolInfo.tool_updatedat;
      dto.originalId = toolInfo.tool_originalid;
      dto.hasUpdate = toolInfo.tool_hasupdate;
      dto.groups = groups;

      // Map default version
      if (defaultVersion) {
        dto.defaultVersion = {
          id: defaultVersion.id,
          versionName: defaultVersion.versionName || `v1.0.0`, // Fallback nếu null
          versionNumber: undefined, // deprecated
          toolName: defaultVersion.toolName,
          toolDescription: defaultVersion.toolDescription,
          parameters: defaultVersion.parameters,
          changeDescription: defaultVersion.changeDescription,
          status: defaultVersion.status,
          createdAt: defaultVersion.createdAt,
          edited: defaultVersion.edited,
        };
      } else {
        dto.defaultVersion = null;
      }

      // Map versions (simple)
      dto.versions = versions.map(version => ({
        id: version.id,
        versionName: version.versionName || `v1.0.0`, // Fallback nếu null
        versionNumber: undefined, // deprecated
        toolName: version.toolName,
      }));

      return dto;
    } catch (error) {
      this.logger.error(`Failed to get user tool by ID: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND, error.message);
    }
  }

  /**
   * Sao chép tool từ admin
   * @param userId ID của người dùng
   * @param cloneDto Dữ liệu sao chép
   * @returns ID của tool đã sao chép
   */
  @Transactional()
  async cloneAdminTool(
    userId: number,
    cloneDto: CloneAdminToolDto,
  ): Promise<string> {
    try {
      const { adminToolId } = cloneDto;

      // Kiểm tra tool admin có tồn tại không
      const adminTool = await this.adminToolRepository.findToolById(adminToolId);
      if (!adminTool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra người dùng đã sao chép tool này chưa
      const existingTool = await this.userToolRepository.findToolByOriginalId(
        adminToolId,
        userId,
      );
      if (existingTool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_ALREADY_EXISTS);
      }

      // Lấy tất cả các phiên bản có trạng thái APPROVED của tool admin
      const adminVersions = await this.adminToolVersionRepository.findVersionsByToolId(adminToolId);
      const approvedVersions = adminVersions.filter(version => version.status === ToolStatusEnum.APPROVED);

      if (approvedVersions.length === 0) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Tạo tool mới cho người dùng
      const newTool = new UserTool();
      newTool.name = cloneDto.customName || adminTool.name;
      newTool.description = cloneDto.customDescription || adminTool.description;
      newTool.userId = userId;
      newTool.originalId = adminToolId;
      newTool.status = ToolStatusEnum.APPROVED;
      newTool.hasUpdate = false;

      // Lưu tool
      const savedTool = await this.userToolRepository.save(newTool);

      // Clone tất cả các phiên bản APPROVED
      for (const adminVersion of approvedVersions) {
        const newVersion = new UserToolVersion();
        newVersion.userId = userId;
        newVersion.originalToolId = savedTool.id;
        newVersion.originalVersionId = adminVersion.id; // Trỏ tới phiên bản gốc
        newVersion.toolName = `${userId}_${adminVersion.toolName}`; // Thêm tiền tố userId_
        newVersion.toolDescription = adminVersion.toolDescription;
        newVersion.parameters = adminVersion.parameters;
        newVersion.changeDescription = cloneDto.changeDescription || `Sao chép từ tool admin ${adminTool.name} - phiên bản ${adminVersion.versionName}`;
        newVersion.status = ToolStatusEnum.APPROVED;
        newVersion.edited = false;

        // Lưu phiên bản
        await this.userToolVersionRepository.save(newVersion);
      }

      return savedTool.id;
    } catch (error) {
      this.logger.error(`Failed to clone admin tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_CREATION_FAILED, error.message);
    }
  }

  /**
   * Cập nhật tool từ phiên bản mới của admin
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật
   * @returns ID của tool đã cập nhật
   */
  @Transactional()
  async updateFromAdmin(
    userId: number,
    updateDto: UpdateFromAdminDto,
  ): Promise<string> {
    try {
      const { userToolId } = updateDto;

      // Kiểm tra tool người dùng có tồn tại không
      const userTool = await this.userToolRepository.findToolById(userToolId, userId);
      if (!userTool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra tool có phải là tool được sao chép từ admin không
      if (!userTool.originalId) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED);
      }

      // Kiểm tra tool admin có tồn tại không
      const adminTool = await this.adminToolRepository.findToolById(userTool.originalId);
      if (!adminTool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Lấy phiên bản mới nhất của tool admin
      const adminVersion = await this.adminToolVersionRepository.findLatestVersionByCreatedAt(adminTool.id);
      if (!adminVersion) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Tạo phiên bản mới
      const newVersion = new UserToolVersion();
      newVersion.userId = userId;
      newVersion.originalToolId = userToolId;
      newVersion.originalVersionId = adminVersion.id; // Trỏ tới phiên bản admin gốc
      newVersion.toolName = adminVersion.toolName;
      newVersion.toolDescription = adminVersion.toolDescription;
      newVersion.parameters = adminVersion.parameters;
      newVersion.changeDescription = `Cập nhật từ phiên bản admin ${adminVersion.versionName}`;
      newVersion.status = ToolStatusEnum.APPROVED;
      newVersion.edited = false;

      // Lưu phiên bản
      await this.userToolVersionRepository.save(newVersion);

      // Cập nhật tool
      userTool.name = adminTool.name;
      userTool.description = adminTool.description;
      userTool.hasUpdate = false;
      userTool.updatedAt = Date.now();

      // Lưu tool
      await this.userToolRepository.save(userTool);

      return userTool.id;
    } catch (error) {
      this.logger.error(`Failed to update from admin: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Sao chép tất cả tool công khai từ admin
   * @param userId ID của người dùng
   * @param cloneDto Dữ liệu sao chép
   * @returns Số lượng tool đã sao chép
   */
  @Transactional()
  async cloneAllPublicTools(
    userId: number,
    cloneDto: CloneAllPublicToolsDto,
  ): Promise<number> {
    try {
      const { namePrefix } = cloneDto;

      // Lấy danh sách tất cả tool công khai của admin từ repository
      const publicTools = await this.adminToolRepository.findAllPublicApprovedTools();

      if (publicTools.length === 0) {
        return 0;
      }

      // Lấy danh sách tool đã được sao chép bởi người dùng từ repository
      const existingTools = await this.userToolRepository.findClonedToolsByUser(userId);

      // Tạo map các tool đã được sao chép
      const existingToolMap = new Map<string, UserTool>();
      existingTools.forEach(tool => {
        if (tool.originalId) {
          existingToolMap.set(tool.originalId, tool);
        }
      });

      // Lọc các tool chưa được sao chép
      const toolsToClone = publicTools.filter(tool => !existingToolMap.has(tool.id));

      if (toolsToClone.length === 0) {
        return 0;
      }

      // Sao chép từng tool
      let clonedCount = 0;
      for (const adminTool of toolsToClone) {
        try {
          // Lấy tất cả các phiên bản có trạng thái APPROVED của tool admin
          const adminVersions = await this.adminToolVersionRepository.findVersionsByToolId(adminTool.id);
          const approvedVersions = adminVersions.filter(version => version.status === ToolStatusEnum.APPROVED);

          if (approvedVersions.length === 0) {
            continue;
          }

          // Tạo tool mới cho người dùng
          const newTool = new UserTool();
          newTool.name = namePrefix ? `${namePrefix}${adminTool.name}` : adminTool.name;
          newTool.description = adminTool.description;
          newTool.userId = userId;
          newTool.originalId = adminTool.id;
          newTool.status = ToolStatusEnum.APPROVED;
          newTool.hasUpdate = false;

          // Lưu tool
          const savedTool = await this.userToolRepository.save(newTool);

          // Clone tất cả các phiên bản APPROVED
          for (const adminVersion of approvedVersions) {
            const newVersion = new UserToolVersion();
            newVersion.userId = userId;
            newVersion.originalToolId = savedTool.id;
            newVersion.originalVersionId = adminVersion.id; // Trỏ tới phiên bản gốc
            newVersion.toolName = `${userId}_${adminVersion.toolName}`; // Thêm tiền tố userId_
            newVersion.toolDescription = adminVersion.toolDescription;
            newVersion.parameters = adminVersion.parameters;
            newVersion.changeDescription = `Sao chép từ tool admin ${adminTool.name} - phiên bản ${adminVersion.versionName}`;
            newVersion.status = ToolStatusEnum.APPROVED;
            newVersion.edited = false;

            // Lưu phiên bản
            await this.userToolVersionRepository.save(newVersion);
          }

          clonedCount++;
        } catch (error) {
          this.logger.error(`Failed to clone tool ${adminTool.id}: ${error.message}`, error.stack);
          // Tiếp tục với tool tiếp theo
        }
      }

      return clonedCount;
    } catch (error) {
      this.logger.error(`Failed to clone all public tools: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_CREATION_FAILED, error.message);
    }
  }

  /**
   * Chỉnh sửa phiên bản tool của người dùng
   * @param userId ID của người dùng
   * @param toolId ID của tool
   * @param versionId ID của phiên bản
   * @param editDto Dữ liệu chỉnh sửa
   * @returns ID của phiên bản mới
   */
  @Transactional()
  async editToolVersion(
    userId: number,
    toolId: string,
    versionId: string,
    editDto: EditToolVersionDto,
  ): Promise<string> {
    try {
      // Kiểm tra tool có tồn tại không và thuộc về người dùng không
      const tool = await this.userToolRepository.findToolById(toolId, userId);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra phiên bản có tồn tại không và thuộc về tool và người dùng không
      const version = await this.userToolVersionRepository.findVersionById(versionId, userId);
      if (!version || version.originalToolId !== toolId) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Cập nhật phiên bản hiện tại (không tạo version mới)
      // Chỉ cập nhật các trường được phép, không được sửa toolName
      const updateData: Partial<UserToolVersion> = {};

      if (editDto.toolDescription !== undefined) {
        updateData.toolDescription = editDto.toolDescription;
      }

      if (editDto.parameters !== undefined) {
        updateData.parameters = editDto.parameters;
      }

      if (editDto.changeDescription !== undefined) {
        updateData.changeDescription = editDto.changeDescription;
      }

      // Đánh dấu là đã được chỉnh sửa
      updateData.edited = true;
      updateData.updatedAt = Date.now();

      // Sử dụng update thay vì save
      const updateResult = await this.userToolVersionRepository.update(
        { id: versionId, userId: userId },
        updateData
      );

      if (updateResult.affected === 0) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_UPDATE_FAILED);
      }

      // Cập nhật thời gian cập nhật cho tool
      tool.updatedAt = Date.now();
      await this.userToolRepository.update(
        { id: toolId, userId: userId },
        { updatedAt: tool.updatedAt }
      );

      return versionId; // Trả về ID của version đã được cập nhật
    } catch (error) {
      this.logger.error(`Failed to edit tool version: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_EDIT_FAILED, error.message);
    }
  }

  /**
   * Khôi phục về phiên bản gốc từ admin
   * @param userId ID của người dùng
   * @param rollbackDto Dữ liệu khôi phục
   * @returns ID của tool đã khôi phục
   */
  @Transactional()
  async rollbackToAdminVersion(
    userId: number,
    rollbackDto: RollbackToAdminVersionDto,
  ): Promise<string> {
    try {
      const { userToolId } = rollbackDto;

      // Kiểm tra tool người dùng có tồn tại không
      const userTool = await this.userToolRepository.findToolById(userToolId, userId);
      if (!userTool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra tool có phải là tool được sao chép từ admin không
      if (!userTool.originalId) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED);
      }

      // Kiểm tra tool admin có tồn tại không
      const adminTool = await this.adminToolRepository.findToolById(userTool.originalId);
      if (!adminTool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Lấy phiên bản mới nhất của tool admin
      const adminVersion = await this.adminToolVersionRepository.findLatestVersionByCreatedAt(adminTool.id);
      if (!adminVersion) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Tạo phiên bản mới
      const newVersion = new UserToolVersion();
      newVersion.userId = userId;
      newVersion.originalToolId = userToolId;
      newVersion.originalVersionId = adminVersion.id; // Trỏ tới phiên bản admin gốc
      newVersion.toolName = adminVersion.toolName;
      newVersion.toolDescription = adminVersion.toolDescription;
      newVersion.parameters = adminVersion.parameters;
      newVersion.changeDescription = `Khôi phục về phiên bản gốc từ admin`;
      newVersion.status = ToolStatusEnum.APPROVED;
      newVersion.edited = false;

      // Lưu phiên bản
      await this.userToolVersionRepository.save(newVersion);

      // Cập nhật tool
      userTool.name = adminTool.name;
      userTool.description = adminTool.description;
      userTool.hasUpdate = false;
      userTool.updatedAt = Date.now();

      // Lưu tool
      await this.userToolRepository.save(userTool);

      return userTool.id;
    } catch (error) {
      this.logger.error(`Failed to rollback to admin version: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_ROLLBACK_FAILED, error.message);
    }
  }



  /**
   * Bật/tắt trạng thái active của tool
   * @param id ID của tool cần cập nhật
   * @param userId ID của người dùng
   * @returns true nếu cập nhật thành công
   */
  @Transactional()
  async toggleToolActive(id: string, userId: number): Promise<boolean> {
    try {
      // Lấy thông tin tool
      const tool = await this.userToolRepository.findToolById(id, userId);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Đảo ngược trạng thái active
      tool.active = !tool.active;
      tool.updatedAt = Date.now();

      // Lưu tool
      await this.userToolRepository.save(tool);

      return true;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trạng thái active: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Sao chép tất cả tool có trạng thái APPROVED và loại PUBLIC
   * @param userId ID của người dùng
   * @returns Số lượng tool đã sao chép
   */
  @Transactional()
  async cloneAllTools(
    userId: number,
  ): Promise<number> {
    try {
      // Lấy danh sách tất cả tool có trạng thái APPROVED và loại PUBLIC từ repository
      const approvedTools = await this.adminToolRepository.findAllPublicApprovedTools();

      if (approvedTools.length === 0) {
        return 0;
      }

      // Lấy danh sách tool đã được sao chép bởi người dùng từ repository
      const existingTools = await this.userToolRepository.findClonedToolsByUser(userId);

      // Tạo map các tool đã được sao chép
      const existingToolMap = new Map<string, UserTool>();
      existingTools.forEach(tool => {
        if (tool.originalId) {
          existingToolMap.set(tool.originalId, tool);
        }
      });

      // Lọc các tool chưa được sao chép
      const toolsToClone = approvedTools.filter(tool => !existingToolMap.has(tool.id));

      if (toolsToClone.length === 0) {
        return 0;
      }

      // Sao chép từng tool
      let clonedCount = 0;
      for (const adminTool of toolsToClone) {
        try {
          // Lấy tất cả các phiên bản có trạng thái APPROVED của tool admin
          const adminVersions = await this.adminToolVersionRepository.findVersionsByToolId(adminTool.id);
          const approvedVersions = adminVersions.filter(version => version.status === ToolStatusEnum.APPROVED);

          if (approvedVersions.length === 0) {
            continue;
          }

          // Tạo tool mới cho người dùng với tiền tố userId_
          const newTool = new UserTool();
          newTool.name = `${userId}_${adminTool.name}`;
          newTool.description = adminTool.description;
          newTool.userId = userId;
          newTool.originalId = adminTool.id;
          newTool.status = ToolStatusEnum.APPROVED;
          newTool.hasUpdate = false;

          // Lưu tool
          const savedTool = await this.userToolRepository.save(newTool);

          // Clone tất cả các phiên bản APPROVED
          for (const adminVersion of approvedVersions) {
            const newVersion = new UserToolVersion();
            newVersion.userId = userId;
            newVersion.originalToolId = savedTool.id;
            newVersion.originalVersionId = adminVersion.id; // Trỏ tới phiên bản gốc
            newVersion.toolName = `${userId}_${adminVersion.toolName}`; // Thêm tiền tố userId_
            newVersion.toolDescription = adminVersion.toolDescription;
            newVersion.parameters = adminVersion.parameters;
            newVersion.changeDescription = `Sao chép từ tool admin ${adminTool.name} - phiên bản ${adminVersion.versionName}`;
            newVersion.status = ToolStatusEnum.APPROVED;
            newVersion.edited = false;

            // Lưu phiên bản
            await this.userToolVersionRepository.save(newVersion);
          }

          clonedCount++;
        } catch (error) {
          this.logger.error(`Failed to clone tool ${adminTool.id}: ${error.message}`, error.stack);
          // Tiếp tục với tool tiếp theo
        }
      }

      return clonedCount;
    } catch (error) {
      this.logger.error(`Failed to clone all tools: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_CREATION_FAILED, error.message);
    }
  }

  /**
   * Cập nhật tất cả các tool có isUpdate=true với phiên bản mới nhất từ admin
   * @param userId ID của người dùng
   * @returns Số lượng tool đã cập nhật
   */
  @Transactional()
  async updateAllToolsWithNewVersion(userId: number): Promise<number> {
    try {
      // Lấy danh sách tất cả tool của người dùng có isUpdate=true từ repository
      const tools = await this.userToolRepository.findToolsWithUpdate(userId);

      if (tools.length === 0) {
        return 0;
      }

      let updatedCount = 0;

      // Cập nhật từng tool
      for (const tool of tools) {
        try {
          if (!tool.originalId) continue;

          // Kiểm tra tool admin có tồn tại không
          const adminTool = await this.adminToolRepository.findToolById(tool.originalId);
          if (!adminTool) continue;

          // Lấy phiên bản mới nhất của tool admin
          const adminVersion = await this.adminToolVersionRepository.findLatestVersionByCreatedAt(adminTool.id);
          if (!adminVersion) continue;

          // Tạo phiên bản mới
          const newVersion = new UserToolVersion();
          newVersion.userId = userId;
          newVersion.originalToolId = tool.id;
          newVersion.originalVersionId = adminVersion.id; // Trỏ tới phiên bản admin gốc
          newVersion.toolName = adminVersion.toolName;
          newVersion.toolDescription = adminVersion.toolDescription;
          newVersion.parameters = adminVersion.parameters;
          newVersion.changeDescription = `Cập nhật từ phiên bản admin ${adminVersion.versionName}`;
          newVersion.status = ToolStatusEnum.APPROVED;
          newVersion.edited = false;

          // Lưu phiên bản
          await this.userToolVersionRepository.save(newVersion);

          // Cập nhật tool
          tool.name = adminTool.name;
          tool.description = adminTool.description;
          tool.hasUpdate = false;
          tool.isUpdate = false;
          tool.updatedAt = Date.now();

          // Lưu tool
          await this.userToolRepository.save(tool);

          updatedCount++;
        } catch (error) {
          this.logger.error(`Lỗi khi cập nhật tool ${tool.id}: ${error.message}`, error.stack);
          // Tiếp tục với tool tiếp theo
        }
      }

      return updatedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật tất cả tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED, error.message);
    }
  }


}
