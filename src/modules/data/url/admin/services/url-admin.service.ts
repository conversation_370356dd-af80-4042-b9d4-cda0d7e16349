import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Url } from '../../entities/url.entity';
import { UrlRepository } from '../../repositories';
import { CreateUrlAdminDto } from '../dto/create-url-admin.dto';
import { UpdateUrlAdminDto } from '../dto/update-url-admin.dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as cheerio from 'cheerio';
import { backOff } from 'exponential-backoff';

import { CrawlAdminDto } from '../dto/crawl-admin.dto';
import { ExtractedMetadata } from '../interfaces/extracted-metadata.interface';
import { FindAllUrlAdminDto } from '@modules/data/url/admin/dto';
import { URL_ERROR_CODES } from '../../exceptions';
import { AppException } from '@/common';

@Injectable()
export class UrlAdminService {
  private readonly logger = new Logger(UrlAdminService.name);

  constructor(
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
    private readonly urlCustomRepository: UrlRepository,
    private readonly httpService: HttpService,
  ) {}

  /**
   * Tìm URL theo ID
   * @param id ID của URL
   * @returns Thông tin URL
   */
  async findUrlById(id: string): Promise<Url> {
    try {
      this.logger.log(`Finding URL with ID: ${id}`);

      if (!id) {
        this.logger.error('URL ID is required');
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_PARAMS,
          'ID URL là bắt buộc',
        );
      }

      const url = await this.urlCustomRepository.findUrlById(id);

      if (!url) {
        this.logger.warn(`URL with ID: ${id} not found`);
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          'Không tìm thấy URL',
        );
      }

      this.logger.log(`URL with ID: ${id} found`);
      return url;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error finding URL by ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        URL_ERROR_CODES.URL_FETCH_FAILED,
        `Không thể lấy thông tin URL: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách URL với phân trang và tìm kiếm
   * @param queryParams Tham số phân trang, sắp xếp và tìm kiếm
   * @returns Danh sách URL đã phân trang
   */
  async findAllUrls(
    queryParams: FindAllUrlAdminDto,
  ): Promise<PaginatedResult<Url>> {
    try {
      this.logger.log(
        `Finding all URLs with params: ${JSON.stringify(queryParams)}`,
      );

      // Sử dụng repository để lấy danh sách URL
      const result = await this.urlCustomRepository.findUrlsByOwner(
        null,
        queryParams,
      );

      this.logger.log(`Found ${result.items.length} URLs`);
      if (result.items.length > 0) {
        this.logger.debug(`First URL: ${JSON.stringify(result.items[0])}`);
      }

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error finding URLs: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_SEARCH_FAILED,
        `Không thể tìm kiếm URL: ${error.message}`,
      );
    }
  }

  /**
   * Tìm kiếm URL theo từ khóa
   * @param keyword Từ khóa tìm kiếm
   * @param limit Số lượng kết quả tối đa
   * @returns Danh sách URL phù hợp
   */
  async searchUrls(keyword: string, limit: number = 10): Promise<Url[]> {
    try {
      this.logger.log(
        `Searching URLs with keyword: ${keyword}, limit: ${limit}`,
      );

      // Kiểm tra tham số đầu vào
      if (!keyword || keyword.trim() === '') {
        this.logger.warn('Empty keyword provided for search');
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_SEARCH_PARAMS,
          'Từ khóa tìm kiếm không được để trống',
        );
      }

      // Tìm kiếm URL theo từ khóa
      const urls = await this.urlCustomRepository.searchUrls(keyword, limit);

      this.logger.log(`Found ${urls.length} URLs matching keyword: ${keyword}`);
      if (urls.length > 0) {
        this.logger.debug(`First search result: ${JSON.stringify(urls[0])}`);
      }

      return urls;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error searching URLs: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_SEARCH_FAILED,
        `Không thể tìm kiếm URL: ${error.message}`,
      );
    }
  }

  /**
   * Tạo URL mới
   * @param createUrlDto Thông tin URL cần tạo
   * @returns URL đã tạo
   */
  async createUrl(createUrlDto: CreateUrlAdminDto): Promise<Url> {
    try {
      this.logger.log(`Creating new URL: ${JSON.stringify(createUrlDto)}`);

      // Kiểm tra URL có hợp lệ không
      try {
        new URL(createUrlDto.url);
      } catch (error) {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_FORMAT,
          'URL không đúng định dạng',
        );
      }

      // Kiểm tra URL đã tồn tại chưa trong toàn bộ hệ thống
      const existingUrl = await this.urlRepository.findOne({
        where: {
          url: createUrlDto.url,
        },
      });

      if (existingUrl) {
        throw new AppException(
          URL_ERROR_CODES.URL_ALREADY_EXISTS,
          'URL này đã tồn tại trong hệ thống',
        );
      }

      // Kiểm tra title và content không được để trống
      if (!createUrlDto.title || createUrlDto.title.trim() === '') {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_CONTENT,
          'Tiêu đề URL không được để trống',
        );
      }

      if (!createUrlDto.content || createUrlDto.content.trim() === '') {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_CONTENT,
          'Nội dung URL không được để trống',
        );
      }

      // Tạo URL mới
      const newUrl = this.urlRepository.create({
        ...createUrlDto,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      // Lưu URL vào database
      return this.urlRepository.save(newUrl);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error creating URL: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_CREATION_FAILED,
        `Không thể tạo URL: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin URL
   * @param id ID của URL cần cập nhật
   * @param updateUrlDto Thông tin cần cập nhật
   * @returns URL đã cập nhật
   */
  async updateUrl(id: string, updateUrlDto: UpdateUrlAdminDto): Promise<Url> {
    try {
      this.logger.log(
        `Updating URL with ID: ${id}, data: ${JSON.stringify(updateUrlDto)}`,
      );

      // Kiểm tra URL tồn tại
      const url = await this.findUrlById(id);

      // Kiểm tra URL có hợp lệ không
      if (updateUrlDto.url) {
        try {
          new URL(updateUrlDto.url);
        } catch (error) {
          throw new AppException(
            URL_ERROR_CODES.URL_INVALID_FORMAT,
            'URL không đúng định dạng',
          );
        }

        // Kiểm tra URL mới đã tồn tại chưa trong toàn bộ hệ thống (nếu khác URL cũ)
        if (updateUrlDto.url !== url.url) {
          const existingUrl = await this.urlRepository.findOne({
            where: {
              url: updateUrlDto.url,
            },
          });

          if (existingUrl) {
            throw new AppException(
              URL_ERROR_CODES.URL_ALREADY_EXISTS,
              'URL này đã tồn tại trong hệ thống',
            );
          }
        }
      }

      // Kiểm tra title không được để trống
      if (
        updateUrlDto.title !== undefined &&
        (updateUrlDto.title === null || updateUrlDto.title.trim() === '')
      ) {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_CONTENT,
          'Tiêu đề URL không được để trống',
        );
      }

      // Kiểm tra content không được để trống
      if (
        updateUrlDto.content !== undefined &&
        (updateUrlDto.content === null || updateUrlDto.content.trim() === '')
      ) {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_CONTENT,
          'Nội dung URL không được để trống',
        );
      }

      // Cập nhật thông tin URL
      Object.assign(url, {
        ...updateUrlDto,
        updatedAt: Date.now(),
      });

      // Lưu URL vào database
      return this.urlRepository.save(url);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error updating URL: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_UPDATE_FAILED,
        `Không thể cập nhật URL: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều URL
   * @param ids Mảng ID của các URL cần xóa
   */
  async deleteUrls(ids: string[]): Promise<void> {
    try {
      this.logger.log(`Deleting URLs with IDs: ${JSON.stringify(ids)}`);

      if (!ids || ids.length === 0) {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_PARAMS,
          'Danh sách ID không được để trống',
        );
      }

      // Kiểm tra tất cả URL tồn tại
      const urls = await this.urlRepository.findByIds(ids);

      if (urls.length !== ids.length) {
        const foundIds = urls.map((url) => url.id);
        const notFoundIds = ids.filter((id) => !foundIds.includes(id));
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          `Không tìm thấy URL với ID: ${notFoundIds.join(', ')}`,
        );
      }

      // Xóa tất cả URL
      await this.urlRepository.remove(urls);

      this.logger.log(`Successfully deleted ${urls.length} URLs`);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error deleting URLs: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_DELETE_FAILED,
        `Không thể xóa URL: ${error.message}`,
      );
    }
  }

  /**
   * Xóa URL (deprecated - sử dụng deleteUrls thay thế)
   * @param id ID của URL cần xóa
   */
  async deleteUrl(id: string): Promise<void> {
    return this.deleteUrls([id]);
  }

  /**
   * Cập nhật trạng thái kích hoạt của URL
   * @param id ID của URL cần cập nhật
   * @param isActive Trạng thái kích hoạt mới
   * @returns URL đã cập nhật
   */
  async updateUrlStatus(id: string, isActive: boolean): Promise<Url> {
    try {
      this.logger.log(
        `Updating URL status with ID: ${id}, isActive: ${isActive}`,
      );

      // Kiểm tra URL tồn tại
      const url = await this.findUrlById(id);

      // Cập nhật trạng thái kích hoạt
      url.isActive = isActive;
      url.updatedAt = Date.now();

      // Lưu URL vào database
      return this.urlRepository.save(url);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error updating URL status: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        URL_ERROR_CODES.URL_UPDATE_FAILED,
        `Không thể cập nhật trạng thái URL: ${error.message}`,
      );
    }
  }

  /**
   * Đảo ngược trạng thái kích hoạt của URL (toggle)
   * @param id ID của URL cần đảo ngược trạng thái
   * @returns URL đã cập nhật
   */
  async toggleUrlStatus(id: string): Promise<Url> {
    try {
      this.logger.log(`Toggling URL status with ID: ${id}`);

      // Kiểm tra URL tồn tại
      const url = await this.findUrlById(id);

      // Log trạng thái trước khi cập nhật
      this.logger.log(
        `URL status before toggle: ${url.isActive}, type: ${typeof url.isActive}`,
      );

      // Đảo ngược trạng thái kích hoạt - đảm bảo giá trị boolean
      const newStatus = url.isActive === true ? false : true;

      const timestamp = Date.now();

      this.logger.log(
        `URL status will be toggled to: ${newStatus}, type: ${typeof newStatus}`,
      );

      // Cập nhật trực tiếp vào entity và lưu
      url.isActive = newStatus;
      url.updatedAt = timestamp;

      // Lưu URL vào database
      const savedUrl = await this.urlRepository.save(url);

      this.logger.log(`URL status updated in database using repository.save()`);
      this.logger.log(
        `URL status after update: ${savedUrl.isActive}, type: ${typeof savedUrl.isActive}`,
      );

      return savedUrl;
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Error toggling URL status: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        URL_ERROR_CODES.URL_UPDATE_FAILED,
        `Không thể đảo ngược trạng thái URL: ${error.message}`,
      );
    }
  }

  /**
   * Trích xuất domain chính từ hostname
   * @param hostname Hostname cần trích xuất
   * @returns Domain chính
   */
  private extractMainDomain(hostname: string): string {
    // Loại bỏ www. nếu có
    let domain = hostname.replace(/^www\./, '');
    return domain;
  }

  /**
   * Chuẩn hóa URL
   * @param url URL cần chuẩn hóa
   * @returns URL đã chuẩn hóa
   */
  private normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);

      // Loại bỏ fragment
      urlObj.hash = '';

      // Loại bỏ các query params không cần thiết
      const paramsToRemove = [
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'fbclid',
        'gclid',
      ];
      const params = new URLSearchParams(urlObj.search);

      paramsToRemove.forEach((param) => {
        if (params.has(param)) {
          params.delete(param);
        }
      });

      urlObj.search = params.toString();

      // Đảm bảo URL kết thúc bằng / nếu không có path
      if (urlObj.pathname === '') {
        urlObj.pathname = '/';
      }

      return urlObj.toString();
    } catch (error) {
      // Nếu không thể chuẩn hóa, trả về URL gốc
      return url;
    }
  }

  /**
   * Kiểm tra quyền crawl theo robots.txt
   * @param url URL cần kiểm tra
   * @returns true nếu được phép crawl, false nếu không
   */
  private async checkRobotsPermission(url: string): Promise<boolean> {
    try {
      const parsedUrl = new URL(url);
      const robotsUrl = `${parsedUrl.protocol}//${parsedUrl.hostname}/robots.txt`;

      this.logger.log(`Kiểm tra robots.txt: ${robotsUrl}`);

      // Sử dụng thư viện exponential-backoff để retry
      const robotsTxt = await backOff(
        async () => {
          const response = await firstValueFrom(
            this.httpService.get(robotsUrl, {
              timeout: 5000,
              headers: {
                'User-Agent': 'Mozilla/5.0',
              },
            }),
          );
          return response.data;
        },
        {
          // Cấu hình backoff
          numOfAttempts: 3,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 5000, // Tối đa 5 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(
              `Attempt ${attemptNumber} failed for robots.txt ${robotsUrl}: ${error.message}`,
            );

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (
              error.response &&
              error.response.status >= 400 &&
              error.response.status < 500 &&
              error.response.status !== 429
            ) {
              return false;
            }

            return true;
          },
        },
      );

      // Phân tích robots.txt đơn giản
      // Kiểm tra xem có bị disallow toàn bộ không
      if (robotsTxt.includes('Disallow: /')) {
        this.logger.warn(`Robots.txt không cho phép crawl: ${url}`);
        return false;
      }

      // Kiểm tra xem URL cụ thể có bị disallow không
      const urlPath = parsedUrl.pathname;
      const disallowLines = robotsTxt
        .split('\n')
        .filter((line: string) => line.trim().startsWith('Disallow:'))
        .map((line: string) => line.split('Disallow:')[1].trim());

      for (const disallowPath of disallowLines) {
        if (disallowPath && urlPath.startsWith(disallowPath)) {
          this.logger.warn(`Robots.txt không cho phép crawl path: ${urlPath}`);
          return false;
        }
      }

      return true;
    } catch (error) {
      // Nếu không tìm thấy robots.txt, cho phép crawl
      this.logger.warn(`Không thể kiểm tra robots.txt: ${error.message}`);
      return true;
    }
  }

  /**
   * Fetch HTML từ URL với retry, chỉ lấy thẻ head
   * @param url URL cần fetch
   * @param maxRetries Số lần retry tối đa
   * @returns HTML của thẻ head
   */
  private async fetchHeadWithRetry(
    url: string,
    maxRetries = 3,
  ): Promise<string> {
    try {
      // Sử dụng thư viện exponential-backoff
      const result = await backOff(
        async () => {
          this.logger.log(`Fetching head from URL: ${url}`);

          // Sử dụng axios với responseType: 'text' để lấy HTML
          const response = await firstValueFrom(
            this.httpService.get(url, {
              headers: {
                'User-Agent': 'Mozilla/5.0',
                Accept: 'text/html',
                'Accept-Language': 'en-US,en;q=0.5',
                'Cache-Control': 'no-cache',
              },
              timeout: 10000, // 10 giây timeout
              maxRedirects: 5,
              responseType: 'text',
            }),
          );

          const html = response.data;

          // Chỉ trích xuất phần head từ HTML
          const headMatch = html.match(/<head[^>]*>([\s\S]*?)<\/head>/i);
          if (headMatch && headMatch[1]) {
            return `<head>${headMatch[1]}</head>`;
          }

          // Nếu không tìm thấy thẻ head, trả về toàn bộ HTML
          return html;
        },
        {
          // Cấu hình backoff
          numOfAttempts: maxRetries,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 30000, // Tối đa 30 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(
              `Attempt ${attemptNumber} failed for ${url}: ${error.message}`,
            );

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (
              error.response &&
              error.response.status >= 400 &&
              error.response.status < 500 &&
              error.response.status !== 429
            ) {
              this.logger.warn(
                `Not retrying due to status code: ${error.response.status}`,
              );
              return false;
            }

            // Retry cho các lỗi khác
            this.logger.log(
              `Retrying... (attempt ${attemptNumber + 1}/${maxRetries})`,
            );
            return true;
          },
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `All retry attempts failed for ${url}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Fetch HTML có giới hạn kích thước
   * @param url URL cần fetch
   * @param maxSize Kích thước tối đa (bytes)
   * @param maxRetries Số lần retry tối đa
   * @returns HTML đã giới hạn kích thước
   */
  private async fetchLimitedHtml(
    url: string,
    maxSize = 50 * 1024,
    maxRetries = 3,
  ): Promise<string> {
    try {
      // Sử dụng thư viện exponential-backoff
      const result = await backOff(
        async () => {
          this.logger.log(
            `Fetching limited HTML (${maxSize} bytes) from URL: ${url}`,
          );

          // Sử dụng axios với responseType: 'text' để lấy HTML
          const response = await firstValueFrom(
            this.httpService.get(url, {
              headers: {
                'User-Agent': 'Mozilla/5.0',
                Accept: 'text/html',
                'Accept-Language': 'en-US,en;q=0.5',
                'Cache-Control': 'no-cache',
              },
              timeout: 15000, // 15 giây timeout
              maxRedirects: 5,
              responseType: 'text',
            }),
          );

          const html = response.data;

          // Giới hạn kích thước HTML
          if (html.length > maxSize) {
            this.logger.log(
              `Limiting HTML size from ${html.length} to ${maxSize} bytes`,
            );
            return html.substring(0, maxSize);
          }

          return html;
        },
        {
          // Cấu hình backoff
          numOfAttempts: maxRetries,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 30000, // Tối đa 30 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(
              `Attempt ${attemptNumber} failed for ${url}: ${error.message}`,
            );

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (
              error.response &&
              error.response.status >= 400 &&
              error.response.status < 500 &&
              error.response.status !== 429
            ) {
              this.logger.warn(
                `Not retrying due to status code: ${error.response.status}`,
              );
              return false;
            }

            // Retry cho các lỗi khác
            this.logger.log(
              `Retrying... (attempt ${attemptNumber + 1}/${maxRetries})`,
            );
            return true;
          },
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `All retry attempts failed for ${url}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Kiểm tra xem URL có phải là trang web đơn giản không
   * @param url URL cần kiểm tra
   * @returns true nếu là trang web đơn giản
   */
  private isSimpleWebsite(url: string): boolean {
    try {
      const hostname = new URL(url).hostname;
      const simplePatterns = [
        'wikipedia.org',
        'github.com',
        'medium.com',
        'news.',
        'blog.',
        'gov.',
        'edu.',
        'org',
      ];
      return simplePatterns.some((pattern) => hostname.includes(pattern));
    } catch {
      return false;
    }
  }

  /**
   * Kiểm tra xem URL có cần trích xuất JSON-LD không
   * @param url URL cần kiểm tra
   * @returns true nếu cần trích xuất JSON-LD
   */
  private needsJsonLd(url: string): boolean {
    try {
      const urlString = url.toLowerCase();
      const jsonLdPatterns = [
        'product',
        'shop',
        'store',
        'ecommerce',
        'article',
        'movie',
        'restaurant',
        'hotel',
        'review',
        'business',
      ];
      return jsonLdPatterns.some((pattern) => urlString.includes(pattern));
    } catch {
      return false;
    }
  }

  /**
   * Kiểm tra xem URL có phải là trang web động không
   * @param url URL cần kiểm tra
   * @returns true nếu là trang web động
   */
  private isDynamicWebsite(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();

      // Danh sách các domain thường sử dụng JavaScript để render nội dung
      const dynamicDomains = [
        'shopee',
        'lazada',
        'tiki',
        'sendo',
        'facebook',
        'twitter',
        'instagram',
        'tiktok',
        'youtube',
        'amazon',
        'ebay',
        'aliexpress',
        'taobao',
        'tmall',
        'jd.com',
        'walmart',
        'target',
        'bestbuy',
        'newegg',
        'etsy',
        'pinterest',
        'linkedin',
        'reddit',
        'spa',
      ];

      // Kiểm tra domain
      if (dynamicDomains.some((domain) => hostname.includes(domain))) {
        this.logger.debug(`Detected dynamic website by domain: ${hostname}`);
        return true;
      }

      // Kiểm tra path có chứa các pattern thường gặp ở trang web động
      const path = urlObj.pathname.toLowerCase();
      const dynamicPaths = [
        '/product/',
        '/item/',
        '/goods/',
        '/detail/',
        '/shop/',
        '/store/',
        '/category/',
        '/search',
        '/listing',
        '/collection',
      ];

      if (dynamicPaths.some((pattern) => path.includes(pattern))) {
        this.logger.debug(`Detected dynamic website by path pattern: ${path}`);
        return true;
      }

      return false;
    } catch {
      return false;
    }
  }

  /**
   * Fetch HTML với phương pháp thích ứng dựa trên loại trang web
   * @param url URL cần fetch
   * @param maxRetries Số lần retry tối đa
   * @returns HTML đã tối ưu
   */
  private async fetchOptimalHtml(url: string, maxRetries = 3): Promise<string> {
    try {
      // Kiểm tra URL để quyết định phương pháp phù hợp
      if (this.isSimpleWebsite(url)) {
        // Trang web đơn giản, chỉ cần phần head
        this.logger.log(`Using head-only fetch for simple website: ${url}`);
        return this.fetchHeadWithRetry(url, maxRetries);
      } else if (this.isDynamicWebsite(url)) {
        // Trang web động, cần lấy nhiều hơn
        this.logger.log(`Using full HTML fetch for dynamic website: ${url}`);
        return this.fetchLimitedHtml(url, 200 * 1024, maxRetries);
      } else if (this.needsJsonLd(url)) {
        // Trang web sử dụng JSON-LD, cần lấy nhiều hơn phần head
        this.logger.log(
          `Using limited HTML fetch (100KB) for JSON-LD website: ${url}`,
        );
        return this.fetchLimitedHtml(url, 100 * 1024, maxRetries);
      } else {
        // Mặc định, lấy HTML có giới hạn
        this.logger.log(`Using default limited HTML fetch (50KB) for: ${url}`);
        return this.fetchLimitedHtml(url, 50 * 1024, maxRetries);
      }
    } catch (error) {
      this.logger.error(
        `Failed to fetch optimal HTML for ${url}: ${error.message}`,
      );
      // Fallback to head-only if optimal fetch fails
      this.logger.log(`Falling back to head-only fetch for ${url}`);
      return this.fetchHeadWithRetry(url, maxRetries);
    }
  }

  /**
   * Trích xuất metadata từ thẻ head
   * @param html HTML của trang web
   * @param url URL của trang web
   * @returns Metadata đã trích xuất
   */
  private extractHeadMetadata(html: string, url: string): ExtractedMetadata {
    const $ = cheerio.load(html);

    // Trích xuất title từ nhiều nguồn
    let title = $('title').text().trim();
    if (!title) {
      title =
        $('meta[property="og:title"]').attr('content') ||
        $('meta[name="twitter:title"]').attr('content') ||
        $('meta[itemprop="name"]').attr('content') ||
        $('h1').first().text().trim() ||
        '';
    }

    // Trích xuất content (description) từ nhiều nguồn
    let content = $('meta[name="description"]').attr('content') || '';
    if (!content) {
      content =
        $('meta[property="og:description"]').attr('content') ||
        $('meta[name="twitter:description"]').attr('content') ||
        $('meta[itemprop="description"]').attr('content') ||
        $('meta[property="description"]').attr('content') ||
        '';
    }

    // Trích xuất tags (keywords) từ nhiều nguồn
    const tags =
      $('meta[name="keywords"]').attr('content') ||
      $('meta[property="article:tag"]').attr('content') ||
      $('meta[property="keywords"]').attr('content') ||
      '';

    this.logger.debug(
      `Extracted metadata from head: title="${title}", content="${content.substring(0, 50)}...", tags="${tags}"`,
    );

    return {
      url,
      title,
      content,
      tags,
    };
  }

  /**
   * Trích xuất metadata từ JSON-LD
   * @param html HTML của trang web
   * @returns Dữ liệu JSON-LD đã phân tích
   */
  private extractJsonLdMetadata(html: string): Array<Record<string, any>> {
    const $ = cheerio.load(html);
    const jsonLdScripts = $('script[type="application/ld+json"]');

    if (jsonLdScripts.length === 0) return [];

    try {
      const jsonLdData: Array<Record<string, any>> = [];
      jsonLdScripts.each((_, element) => {
        try {
          const jsonContent = $(element).html();
          if (jsonContent) {
            const parsed = JSON.parse(jsonContent);
            jsonLdData.push(parsed);
          }
        } catch (e) {
          this.logger.warn(`Failed to parse JSON-LD: ${e.message}`);
        }
      });

      this.logger.debug(`Extracted ${jsonLdData.length} JSON-LD objects`);
      return jsonLdData;
    } catch (error) {
      this.logger.warn(`Error extracting JSON-LD: ${error.message}`);
      return [];
    }
  }

  /**
   * Kiểm tra metadata có hợp lệ không (có title và content)
   * @param metadata Metadata cần kiểm tra
   * @returns true nếu metadata hợp lệ, false nếu không
   */
  private isValidMetadata(metadata: ExtractedMetadata): boolean {
    // Kiểm tra title có tồn tại và không rỗng
    const hasTitle = Boolean(metadata.title && metadata.title.trim() !== '');

    // Kiểm tra content có tồn tại và không rỗng
    const hasContent = Boolean(
      metadata.content && metadata.content.trim() !== '',
    );

    // Log kết quả kiểm tra
    if (!hasTitle || !hasContent) {
      this.logger.warn(
        `Invalid metadata for URL ${metadata.url}: hasTitle=${hasTitle}, hasContent=${hasContent}`,
      );
    }

    return hasTitle && hasContent;
  }

  /**
   * Lưu metadata vào cơ sở dữ liệu
   * @param employeeId ID của admin (nếu có)
   * @param ownedBy ID của người dùng sở hữu URL (nếu có)
   * @param metadata Metadata đã trích xuất
   * @returns true nếu lưu thành công, false nếu không
   */
  private async saveMetadata(
    employeeId: number | null,
    ownedBy: number | null,
    metadata: ExtractedMetadata,
  ): Promise<boolean> {
    try {
      // Kiểm tra metadata có hợp lệ không
      if (!this.isValidMetadata(metadata)) {
        this.logger.warn(`Skipping save for invalid metadata: ${metadata.url}`);
        return false;
      }

      // Kiểm tra xem URL đã tồn tại trong cơ sở dữ liệu chưa
      const existingUrl = await this.urlRepository.findOne({
        where: { url: metadata.url },
      });

      if (existingUrl) {
        // Nếu đã tồn tại, cập nhật metadata
        existingUrl.title = metadata.title;
        existingUrl.content = metadata.content;
        // Lưu tags vào trường content nếu cần
        if (metadata.tags) {
          existingUrl.content = `${metadata.content}\n\nTags: ${metadata.tags}`;
        }
        existingUrl.updatedAt = Date.now();

        await this.urlRepository.save(existingUrl);
        this.logger.log(`Đã cập nhật metadata cho URL: ${metadata.url}`);
      } else {
        // Nếu chưa tồn tại, tạo mới
        const newUrl = new Url();
        newUrl.url = metadata.url;
        newUrl.title = metadata.title;
        newUrl.content = metadata.content;
        // Lưu tags vào trường content nếu cần
        if (metadata.tags) {
          newUrl.content = `${metadata.content}\n\nTags: ${metadata.tags}`;
        }

        // Nếu có ownedBy, sử dụng nó, nếu không sử dụng employeeId
        if (ownedBy !== null) {
          newUrl.ownedBy = ownedBy;
        } else if (employeeId !== null) {
          newUrl.ownedBy = employeeId;
        }

        newUrl.createdAt = Date.now();
        newUrl.updatedAt = Date.now();

        await this.urlRepository.save(newUrl);
        this.logger.log(`Đã lưu metadata mới cho URL: ${metadata.url}`);
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lưu metadata cho URL ${metadata.url}: ${error.message}`,
      );
      return false;
    }
  }

  /**
   * Trích xuất các URL con từ một URL
   * @param url URL cần trích xuất
   * @returns Danh sách các URL con
   */
  private async extractChildUrls(url: string): Promise<string[]> {
    try {
      // Fetch HTML với phương pháp thích ứng để lấy các URL con
      const html = await this.fetchOptimalHtml(url);
      const $ = cheerio.load(html);
      const baseUrl = new URL(url);
      const childUrls = new Set<string>();

      // Lấy tất cả các thẻ a có href
      $('a[href]').each((_: any, element: any) => {
        const href = $(element).attr('href');

        // Bỏ qua các URL không hợp lệ
        if (
          !href ||
          href.startsWith('#') ||
          href.startsWith('javascript:') ||
          href.startsWith('mailto:')
        ) {
          return;
        }

        try {
          // Xử lý URL để có URL tuyệt đối
          let absoluteUrl: string;

          if (href.startsWith('http://') || href.startsWith('https://')) {
            absoluteUrl = href;
          } else if (href.startsWith('//')) {
            absoluteUrl = `${baseUrl.protocol}${href}`;
          } else if (href.startsWith('/')) {
            absoluteUrl = `${baseUrl.origin}${href}`;
          } else {
            let basePath = baseUrl.pathname;
            if (!basePath.endsWith('/')) {
              basePath = basePath.substring(0, basePath.lastIndexOf('/') + 1);
            }
            absoluteUrl = `${baseUrl.origin}${basePath}${href}`;
          }

          // Chuẩn hóa URL
          const normalizedUrl = this.normalizeUrl(absoluteUrl);

          // Kiểm tra domain
          const urlObj = new URL(normalizedUrl);
          const urlDomain = this.extractMainDomain(urlObj.hostname);
          const baseDomain = this.extractMainDomain(baseUrl.hostname);

          if (urlDomain === baseDomain) {
            childUrls.add(normalizedUrl);
          }
        } catch (urlError) {
          // Bỏ qua URL lỗi
        }
      });

      return Array.from(childUrls);
    } catch (error) {
      this.logger.warn(
        `Lỗi khi trích xuất URL con từ ${url}: ${error.message}`,
      );
      return [];
    }
  }

  /**
   * Crawl URL và các URL con để lấy metadata
   * @param employeeId ID của admin
   * @param crawlDto Thông tin URL cần crawl
   * @returns Danh sách các URL với metadata
   */
  async crawlUrl(
    employeeId: number,
    crawlDto: CrawlAdminDto,
  ): Promise<{
    status: string;
    message: string;
    urlsProcessed?: number;
    errors?: string[];
  }> {
    // Mảng lưu trữ các lỗi gặp phải
    const errors: string[] = [];

    try {
      this.logger.log(`===== BẮT ĐẦU CRAWL URL =====`);
      this.logger.log(
        `Admin: ${employeeId}, URL: ${crawlDto.url}, Độ sâu: ${crawlDto.depth}`,
      );

      // Kiểm tra URL có hợp lệ không
      try {
        const urlObj = new URL(crawlDto.url);
        this.logger.log(`URL hợp lệ: ${urlObj.href}`);

        // Chuẩn hóa URL
        const normalizedUrl = this.normalizeUrl(crawlDto.url);
        if (normalizedUrl !== crawlDto.url) {
          this.logger.log(`URL đã được chuẩn hóa: ${normalizedUrl}`);
          crawlDto.url = normalizedUrl;
        }

        // Kiểm tra robots.txt nếu không bỏ qua
        if (!crawlDto.ignoreRobotsTxt) {
          const isAllowed = await this.checkRobotsPermission(crawlDto.url);
          if (!isAllowed) {
            const robotsMsg = `URL không được phép crawl theo robots.txt: ${crawlDto.url}`;
            this.logger.error(robotsMsg);
            errors.push(robotsMsg);
            throw new AppException(URL_ERROR_CODES.URL_CRAWL_FAILED, robotsMsg);
          }
        } else {
          this.logger.log(`Bỏ qua kiểm tra robots.txt theo yêu cầu người dùng`);
        }
      } catch (error) {
        if (error instanceof AppException) {
          throw error;
        }

        const errorMsg = `URL không đúng định dạng: ${error.message}`;
        this.logger.error(errorMsg);
        errors.push(errorMsg);
        throw new AppException(URL_ERROR_CODES.URL_INVALID_FORMAT, errorMsg);
      }

      // Giới hạn số lượng URL tối đa
      const MAX_URLS = crawlDto.maxUrls || 20;
      this.logger.log(
        `Giới hạn tối đa: ${MAX_URLS} URLs, độ sâu tối đa: ${crawlDto.depth}`,
      );

      // Khởi tạo các biến cần thiết
      const visitedUrls = new Set<string>();
      const urlsToVisit: Array<{ url: string; depth: number }> = [
        { url: crawlDto.url, depth: 0 },
      ];
      const processedUrls: ExtractedMetadata[] = [];

      // Biến đếm số lượng URL đã thử crawl
      let attemptedUrls = 0;

      this.logger.log(`===== BẮT ĐẦU VÒNG LẶP CRAWL URL =====`);

      // Crawl hết URL theo độ sâu
      while (urlsToVisit.length > 0 && visitedUrls.size < MAX_URLS) {
        const currentItem = urlsToVisit.shift();
        if (!currentItem) {
          const errorMsg = `Phát hiện item null trong hàng đợi URL`;
          this.logger.warn(errorMsg);
          errors.push(errorMsg);
          continue;
        }

        const { url, depth } = currentItem;
        attemptedUrls++;

        this.logger.log(
          `\n----- Xử lý URL #${attemptedUrls}: ${url} (độ sâu: ${depth}) -----`,
        );

        // Nếu URL đã được xử lý, bỏ qua
        if (visitedUrls.has(url)) {
          const skipMsg = `URL đã được xử lý trước đó, bỏ qua: ${url}`;
          this.logger.log(skipMsg);
          continue;
        }

        // Đánh dấu URL đã được xử lý
        visitedUrls.add(url);
        this.logger.log(`Đã thêm URL vào danh sách đã xử lý: ${url}`);

        // Không cần delay giữa các request vì thư viện exponential-backoff đã xử lý
        // Mỗi request sẽ được xử lý với jitter và backoff tự động

        try {
          // Fetch HTML từ URL với phương pháp thích ứng
          this.logger.log(`Bắt đầu tải HTML từ URL: ${url}`);
          const html = await this.fetchOptimalHtml(url);

          // Trích xuất metadata từ thẻ head
          this.logger.log(`Trích xuất metadata từ HTML của URL: ${url}`);
          const metadata = this.extractHeadMetadata(html, url);

          // Trích xuất metadata từ JSON-LD nếu có
          this.logger.log(`Kiểm tra JSON-LD trong URL: ${url}`);
          const jsonLdData = this.extractJsonLdMetadata(html);

          // Kết hợp metadata từ JSON-LD nếu có
          if (jsonLdData && jsonLdData.length > 0) {
            this.logger.log(
              `Tìm thấy ${jsonLdData.length} đối tượng JSON-LD, bổ sung metadata`,
            );

            // Lấy đối tượng JSON-LD đầu tiên
            const jsonLd = jsonLdData[0];

            // Bổ sung title nếu chưa có
            if (!metadata.title && jsonLd.name) {
              metadata.title = jsonLd.name;
              this.logger.debug(`Bổ sung title từ JSON-LD: ${metadata.title}`);
            }

            // Bổ sung content nếu chưa có
            if (!metadata.content && jsonLd.description) {
              metadata.content = jsonLd.description;
              this.logger.debug(
                `Bổ sung content từ JSON-LD: ${metadata.content.substring(0, 50)}...`,
              );
            }

            // Bổ sung tags nếu có
            if (!metadata.tags && jsonLd.keywords) {
              if (Array.isArray(jsonLd.keywords)) {
                metadata.tags = jsonLd.keywords.join(', ');
              } else if (typeof jsonLd.keywords === 'string') {
                metadata.tags = jsonLd.keywords;
              }
              this.logger.debug(`Bổ sung tags từ JSON-LD: ${metadata.tags}`);
            }
          }

          // Log thông tin metadata đã trích xuất
          this.logger.log(
            `Đã trích xuất metadata: title="${metadata.title}", content="${metadata.content.substring(0, 50)}...", tags="${metadata.tags}"`,
          );

          // Lưu metadata vào cơ sở dữ liệu và kiểm tra kết quả
          const saveSuccess = await this.saveMetadata(
            employeeId,
            crawlDto.ownedBy || null,
            metadata,
          );

          // Chỉ thêm vào danh sách đã xử lý nếu lưu thành công
          if (saveSuccess) {
            processedUrls.push(metadata);
            this.logger.log(
              `Đã lưu metadata vào cơ sở dữ liệu cho URL: ${url}`,
            );
          } else {
            this.logger.warn(`Bỏ qua lưu URL do metadata không hợp lệ: ${url}`);
          }

          // Nếu chưa đạt độ sâu tối đa, tiếp tục crawl các URL con
          if (depth < crawlDto.depth) {
            // Trích xuất các URL con
            this.logger.log(`Bắt đầu trích xuất các URL con từ URL: ${url}`);
            const childUrls = await this.extractChildUrls(url);
            this.logger.log(
              `Đã tìm thấy ${childUrls.length} URL con từ URL: ${url}`,
            );

            // Thêm các URL con vào hàng đợi
            for (const childUrl of childUrls) {
              if (
                !visitedUrls.has(childUrl) &&
                urlsToVisit.length + visitedUrls.size < MAX_URLS
              ) {
                urlsToVisit.push({ url: childUrl, depth: depth + 1 });
                this.logger.log(
                  `Đã thêm URL con vào hàng đợi: ${childUrl} (độ sâu: ${depth + 1})`,
                );
              }
            }
          }
        } catch (fetchError) {
          const errorMsg = `Lỗi khi xử lý URL ${url}: ${fetchError.message}`;
          this.logger.warn(errorMsg);
          errors.push(errorMsg);
          // Tiếp tục với URL tiếp theo
          continue;
        }
      }

      // Tạo thông báo kết quả
      let resultMessage = '';
      if (processedUrls.length > 0) {
        resultMessage = `Đã crawl thành công ${processedUrls.length} URL từ ${crawlDto.url} với độ sâu ${crawlDto.depth} (giới hạn: ${MAX_URLS} URLs)`;
      } else {
        resultMessage = `Không crawl được URL nào từ ${crawlDto.url} với độ sâu ${crawlDto.depth} (giới hạn: ${MAX_URLS} URLs)`;
      }

      this.logger.log(`===== KẾT THÚC CRAWL URL =====`);
      this.logger.log(resultMessage);

      return {
        status: 'success',
        message: resultMessage,
        urlsProcessed: processedUrls.length,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      // Xử lý lỗi
      const errorMsg =
        error instanceof AppException
          ? error.message
          : `Lỗi không xác định: ${error.message}`;
      this.logger.error(`Crawl URL thất bại: ${errorMsg}`);

      return {
        status: 'error',
        message: errorMsg,
        urlsProcessed: 0,
        errors: errors.length > 0 ? [...errors, errorMsg] : [errorMsg],
      };
    }
  }
}
