import { Controller, Delete, Get, Param, ParseIntPipe, Post, Put, UseGuards, Logger } from '@nestjs/common';
import { SkipValidation } from '../decorators';
import { ApiBearerAuth, ApiBody, ApiCreatedResponse, ApiExtraModels, ApiOkResponse, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { BusinessIntegrationService } from '@modules/business/user/services';

import { ApiResponseDto } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { CurrentUser } from '@modules/auth/decorators';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ErrorCode } from '@common/exceptions';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import {
  CreateIntegratedBusinessDto, DeleteIntegratedBusinessDto,
  IntegratedBusinessResponseDto,
  UpdateIntegratedBusinessDto,
  CustomFieldUpdateItem,
  CreateIntegratedBusinessWithoutClassificationDto,
  IntegratedBusinessWithoutClassificationResponseDto
} from '../dto/business-integrated.dto';
import {
  CreateCustomFieldDto,
  UpdateCustomFieldDto,
  CreateCustomGroupFormDto,
  UpdateCustomGroupFormDto,
  BusinessCreateProductDto,
  BusinessUpdateProductDto,
  CreateClassificationDto,
  UpdateClassificationDto,
  ClassificationResponseDto
} from '../dto';

/**
 * Controller xử lý các endpoint tích hợp cho nhiều chức năng business
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS)
@Controller('user/business-integration')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  IntegratedBusinessResponseDto,
  IntegratedBusinessWithoutClassificationResponseDto,
  BusinessCreateProductDto,
  BusinessUpdateProductDto,
  CreateCustomGroupFormDto,
  UpdateCustomGroupFormDto,
  CreateCustomFieldDto,
  UpdateCustomFieldDto,
  CustomFieldUpdateItem,
  CreateClassificationDto,
  UpdateClassificationDto,
  ClassificationResponseDto,
  CreateIntegratedBusinessWithoutClassificationDto
)
export class BusinessIntegrationController {
  private readonly logger = new Logger(BusinessIntegrationController.name);

  constructor(private readonly businessIntegrationService: BusinessIntegrationService) {}

  /**
   * Tạo tích hợp mới bao gồm sản phẩm, nhóm trường, các trường tùy chỉnh và phân loại sản phẩm
   * @param createDto DTO chứa thông tin tạo tích hợp
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin tích hợp đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo tích hợp mới (sản phẩm, nhóm trường, trường tùy chỉnh, phân loại sản phẩm)' })
  @ApiCreatedResponse({
    description: 'Tích hợp đã được tạo thành công',
    schema: ApiResponseDto.getSchema(IntegratedBusinessResponseDto),
  })
  @ApiBody({
    type: CreateIntegratedBusinessDto,
    description: 'Thông tin tạo tích hợp mới',
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.INTEGRATION_CREATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async create(
    @SkipValidation() requestBody: any,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<IntegratedBusinessResponseDto>> {
    try {
      this.logger.log(`Tạo tích hợp mới với userId=${userId}, data=${JSON.stringify(requestBody)}`);

      // Chuyển đổi dữ liệu đầu vào thành DTO
      const createDto: CreateIntegratedBusinessDto = {
        product: requestBody.product,
        groupForm: requestBody.groupForm,
        customFields: requestBody.customFields,
        classifications: requestBody.classifications
      };

      const result = await this.businessIntegrationService.create(createDto, userId);
      return ApiResponseDto.created(result, 'Tích hợp đã được tạo thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi tạo tích hợp: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật tích hợp
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin tích hợp đã cập nhật
   */
  @Put()
  @ApiOperation({ summary: 'Cập nhật tích hợp (sản phẩm, nhóm trường, trường tùy chỉnh, phân loại sản phẩm)' })
  @ApiOkResponse({
    description: 'Tích hợp đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(IntegratedBusinessResponseDto),
  })
  @ApiBody({
    type: UpdateIntegratedBusinessDto,
    description: 'Thông tin cập nhật tích hợp',
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.INTEGRATION_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async update(
    @SkipValidation() requestBody: any,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<IntegratedBusinessResponseDto>> {
    try {
      this.logger.log(`Cập nhật tích hợp với userId=${userId}, data=${JSON.stringify(requestBody)}`);

      // Chuyển đổi dữ liệu đầu vào thành DTO
      const updateDto: UpdateIntegratedBusinessDto = {
        productId: requestBody.productId,
        product: requestBody.product,
        groupFormId: requestBody.groupFormId,
        groupForm: requestBody.groupForm,
        customFields: requestBody.customFields,
        customFieldsToDelete: requestBody.customFieldsToDelete,
        classifications: requestBody.classifications,
        classificationsToDelete: requestBody.classificationsToDelete
      };

      const result = await this.businessIntegrationService.update(updateDto, userId);
      return ApiResponseDto.success(result, 'Tích hợp đã được cập nhật thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật tích hợp: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa tích hợp
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo xóa thành công
   */
  @Delete()
  @ApiOperation({ summary: 'Xóa tích hợp (sản phẩm, nhóm trường, trường tùy chỉnh)' })
  @ApiOkResponse({
    description: 'Tích hợp đã được xóa thành công',
    schema: ApiResponseDto.getSchema(Object),
  })
  @ApiBody({
    type: DeleteIntegratedBusinessDto,
    description: 'Thông tin xóa tích hợp',
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.INTEGRATION_DELETION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async delete(
    @SkipValidation() requestBody: any,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<null>> {
    try {
      this.logger.log(`Xóa tích hợp với userId=${userId}, data=${JSON.stringify(requestBody)}`);

      // Chuyển đổi dữ liệu đầu vào thành DTO
      const deleteDto: DeleteIntegratedBusinessDto = {
        productId: requestBody.productId
      };

      await this.businessIntegrationService.delete(deleteDto, userId);
      return ApiResponseDto.success(null, 'Tích hợp đã được xóa thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi xóa tích hợp: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy chi tiết tích hợp
   * @param productId ID của sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin chi tiết tích hợp
   */
  @Get(':productId')
  @ApiOperation({ summary: 'Lấy chi tiết tích hợp (sản phẩm, nhóm trường, trường tùy chỉnh)' })
  @ApiParam({
    name: 'productId',
    description: 'ID của sản phẩm cần lấy chi tiết tích hợp',
    type: 'number',
    example: 123
  })
  @ApiOkResponse({
    description: 'Chi tiết tích hợp',
    schema: ApiResponseDto.getSchema(IntegratedBusinessResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.INTEGRATION_FIND_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async getDetail(
    @Param('productId', ParseIntPipe) productId: number,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<IntegratedBusinessResponseDto>> {
    const result = await this.businessIntegrationService.getDetail(productId, userId);
    return ApiResponseDto.success(result, 'Lấy chi tiết tích hợp thành công');
  }

  /**
   * Tạo tích hợp mới bao gồm sản phẩm, nhóm trường, các trường tùy chỉnh (không bao gồm phân loại sản phẩm)
   * @param createDto DTO chứa thông tin tạo tích hợp không bao gồm phân loại
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin tích hợp đã tạo
   */
  @Post('without-classification')
  @ApiOperation({ summary: 'Tạo tích hợp mới (sản phẩm, nhóm trường, trường tùy chỉnh) không bao gồm phân loại sản phẩm' })
  @ApiCreatedResponse({
    description: 'Tích hợp đã được tạo thành công',
    schema: ApiResponseDto.getSchema(IntegratedBusinessWithoutClassificationResponseDto),
  })
  @ApiBody({
    type: CreateIntegratedBusinessWithoutClassificationDto,
    description: 'Thông tin tạo tích hợp mới không bao gồm phân loại',
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.INTEGRATION_CREATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async createWithoutClassification(
    @SkipValidation() requestBody: any,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<IntegratedBusinessWithoutClassificationResponseDto>> {
    try {
      this.logger.log(`Tạo tích hợp mới không bao gồm phân loại với userId=${userId}, data=${JSON.stringify(requestBody)}`);

      // Chuyển đổi dữ liệu đầu vào thành DTO
      const createDto: CreateIntegratedBusinessWithoutClassificationDto = {
        product: requestBody.product,
        groupForm: requestBody.groupForm,
        customFields: requestBody.customFields
      };

      const result = await this.businessIntegrationService.createWithoutClassification(createDto, userId);
      return ApiResponseDto.created(result, 'Tích hợp đã được tạo thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi tạo tích hợp không bao gồm phân loại: ${error.message}`, error.stack);
      throw error;
    }
  }
}