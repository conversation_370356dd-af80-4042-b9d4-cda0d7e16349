import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * D<PERSON> cho request cập nhật nhóm trường tùy chỉnh
 */
export class UpdateCustomGroupFormDto {
  /**
   * Nhãn hiển thị
   * @example "Thông tin cá nhân cập nhật"
   */
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Thông tin cá nhân cập nhật',
    maxLength: 255,
    required: true,
  })
  @IsNotEmpty({ message: 'Nhãn hiển thị không được để trống' })
  @IsString({ message: 'Nhãn hiển thị phải là chuỗi' })
  @MaxLength(255, { message: 'Nhãn hiển thị không được vượt quá 255 ký tự' })
  label: string;



  // Trường userId sẽ được lấy từ thông tin người dùng đăng nhập
  // và được gán trong controller
  userId?: number;
}
