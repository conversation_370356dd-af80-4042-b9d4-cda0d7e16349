import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp trường tùy chỉnh
 */
export enum CustomFieldSortField {
  ID = 'id',
  COMPONENT = 'component',
  CONFIG_ID = 'configId',
  LABEL = 'label',
  TYPE = 'type',
  CREATED_AT = 'createAt',
  STATUS = 'status',
}

/**
 * DTO cho các tham số truy vấn danh sách trường tùy chỉnh
 */
export class QueryCustomFieldDto extends QueryDto {
  /**
   * ID người dùng để lọc
   * @example 1001
   */
  @ApiProperty({
    description: 'ID người dùng để lọc',
    example: 1001,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;

  /**
   * ID nhân viên để lọc
   * @example 101
   */
  @ApiProperty({
    description: 'ID nhân viên để lọc',
    example: 101,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  employeeId?: number;

  /**
   * Loại trường để lọc
   * @example "text"
   */
  @ApiProperty({
    description: 'Loại trường để lọc',
    example: 'text',
    required: false,
  })
  @IsOptional()
  @IsString()
  type?: string;

  /**
   * Trường sắp xếp
   * @example "createAt"
   */
  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: CustomFieldSortField,
    example: CustomFieldSortField.CREATED_AT,
    default: CustomFieldSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(CustomFieldSortField, {
    message: `Trường sắp xếp phải là một trong các giá trị: ${Object.values(CustomFieldSortField).join(', ')}`,
  })
  override sortBy?: CustomFieldSortField = CustomFieldSortField.CREATED_AT;

  /**
   * Hướng sắp xếp
   * @example "DESC"
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection, {
    message: `Hướng sắp xếp phải là một trong các giá trị: ${Object.values(SortDirection).join(', ')}`,
  })
  override sortDirection?: SortDirection = SortDirection.DESC;
}
