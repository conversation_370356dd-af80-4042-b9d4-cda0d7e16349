import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * DTO cho thông tin agent
 */
export class AgentInfoDto {
  @Expose()
  @ApiProperty({
    description: 'ID của agent',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @Expose()
  @ApiProperty({
    description: 'Tên của agent',
    example: 'Agent hỗ trợ khách hàng',
    nullable: true,
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Ảnh đại diện của agent',
    example: 'https://example.com/agent-avatar.jpg',
    nullable: true,
  })
  avatar: string | null;
}

/**
 * DTO cho thông tin user
 */
export class UserInfoDto {
  @Expose()
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'T<PERSON><PERSON> đ<PERSON>y đủ của người dùng',
    example: '<PERSON>uy<PERSON><PERSON>',
    nullable: true,
  })
  fullName: string;

  @Expose()
  @ApiProperty({
    description: 'Email của người dùng',
    example: '<EMAIL>',
    nullable: true,
  })
  email: string;

  @Expose()
  @ApiProperty({
    description: 'Số điện thoại của người dùng',
    example: '0912345678',
    nullable: true,
  })
  phoneNumber: string;

  @Expose()
  @ApiProperty({
    description: 'Ảnh đại diện của người dùng',
    example: 'https://example.com/user-avatar.jpg',
    nullable: true,
  })
  avatar: string | null;
}

/**
 * DTO cho response khi lấy thông tin khách hàng chuyển đổi
 */
export class UserConvertCustomerResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID khách hàng',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Ảnh đại diện',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatar: string;

  @Expose()
  @ApiProperty({
    description: 'Tên khách hàng',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Email khách hàng',
    example: { primary: '<EMAIL>', secondary: '<EMAIL>' },
    nullable: true,
  })
  email: any;

  @Expose()
  @ApiProperty({
    description: 'Số điện thoại khách hàng',
    example: '0912345678',
    nullable: true,
  })
  phone: string;

  @Expose()
  @ApiProperty({
    description: 'Nền tảng nguồn',
    example: 'Facebook',
    nullable: true,
  })
  platform: string;

  @Expose()
  @ApiProperty({
    description: 'Múi giờ của khách hàng',
    example: 'Asia/Ho_Chi_Minh',
    nullable: true,
  })
  timezone: string;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1741708800000,
  })
  updatedAt: number;

  @Expose()
  @ApiProperty({
    description: 'ID người dùng sở hữu khách hàng',
    example: 1001,
    nullable: true,
  })
  userId: number;

  @Expose()
  @ApiProperty({
    description: 'ID agent hỗ trợ khách hàng',
    example: '550e8400-e29b-41d4-a716-446655440000',
    nullable: true,
  })
  agentId: string;

  @Expose()
  @ApiProperty({
    description: 'Thông tin chi tiết về agent hỗ trợ khách hàng',
    type: AgentInfoDto,
    nullable: true,
  })
  @Type(() => AgentInfoDto)
  agent?: AgentInfoDto;

  @Expose()
  @ApiProperty({
    description: 'Thông tin chi tiết về người dùng sở hữu khách hàng',
    type: UserInfoDto,
    nullable: true,
  })
  @Type(() => UserInfoDto)
  user?: UserInfoDto;

  @Expose()
  @ApiProperty({
    description: 'Trường tùy chỉnh',
    example: [
      { key: 'address', value: 'Hà Nội' },
      { key: 'job', value: 'Developer' },
    ],
  })
  metadata: any;
}

/**
 * DTO cho danh sách khách hàng chuyển đổi
 */
export class UserConvertCustomerListItemDto {
  @Expose()
  @ApiProperty({
    description: 'ID khách hàng',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Ảnh đại diện',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatar: string;

  @Expose()
  @ApiProperty({
    description: 'Tên khách hàng',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Email khách hàng',
    example: { primary: '<EMAIL>' },
    nullable: true,
  })
  email: any;

  @Expose()
  @ApiProperty({
    description: 'Số điện thoại khách hàng',
    example: '0912345678',
    nullable: true,
  })
  phone: string;

  @Expose()
  @ApiProperty({
    description: 'Nền tảng nguồn',
    example: 'Facebook',
    nullable: true,
  })
  platform: string;

  @Expose()
  @ApiProperty({
    description: 'Múi giờ của khách hàng',
    example: 'Asia/Ho_Chi_Minh',
    nullable: true,
  })
  timezone: string;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1741708800000,
  })
  updatedAt: number;


  @Expose()
  @ApiProperty({
    description: 'metadata',
    example: 1001,
    nullable: true,
  })
  metadata: any;
}
