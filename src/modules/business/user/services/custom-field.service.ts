import { Injectable, Logger } from '@nestjs/common';
import { CustomFieldRepository, CustomGroupFormRepository, CustomGroupFormFieldRepository } from '@modules/business/repositories';
import { CreateCustomFieldDto, CustomFieldResponseDto, QueryCustomFieldDto, CustomFieldListItemDto, CustomFieldDetailResponseDto, LinkedGroupDto, UpdateCustomFieldDto, ComponentListResponseDto } from '../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CustomField, CustomGroupForm, CustomGroupFormField, CustomFieldStatus } from '@modules/business/entities';
import { Transactional } from 'typeorm-transactional';
import { ValidationHelper } from '../helpers';
import { plainToInstance } from 'class-transformer';
import { EntityStatusEnum } from '@modules/business/enums';
import { PaginatedResult } from '@common/response/api-response-dto';
import { DEFAULT_COMPONENTS } from '@modules/business/constants/component.constants';

/**
 * Service xử lý logic nghiệp vụ cho trường tùy chỉnh
 */
@Injectable()
export class CustomFieldService {
  private readonly logger = new Logger(CustomFieldService.name);

  constructor(
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly customGroupFormRepository: CustomGroupFormRepository,
    private readonly customGroupFormFieldRepository: CustomGroupFormFieldRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Tạo trường tùy chỉnh mới
   * @param createDto DTO chứa thông tin tạo trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh đã tạo
   */
  @Transactional()
  async create(createDto: CreateCustomFieldDto): Promise<CustomFieldResponseDto> {
    try {
      this.logger.log(`Tạo trường tùy chỉnh mới: ${JSON.stringify(createDto)}`);

      // Kiểm tra tính hợp lệ của dữ liệu
      this.validationHelper.validateCreateCustomFieldData(createDto);

      // Kiểm tra configId đã tồn tại cho cùng một người dùng chưa
      const existingField = await this.customFieldRepository.findOne({
        where: {
          configId: createDto.configId
        },
      });

      // Kiểm tra và xác thực configId không trùng lặp cho cùng một người dùng
      this.validationHelper.validateConfigIdNotExists(existingField, createDto.configId, createDto.userId);

      // Kiểm tra formGroupId nếu có
      let formGroup: CustomGroupForm | null = null;
      if (createDto.formGroupId) {
        formGroup = await this.customGroupFormRepository.findById(createDto.formGroupId);
        this.validationHelper.validateGroupFormExists(formGroup, createDto.formGroupId);
      }

      // Tạo entity mới
      const newCustomField = new CustomField();
      newCustomField.component = createDto.component;
      newCustomField.configId = createDto.configId;
      newCustomField.label = createDto.label;
      newCustomField.type = createDto.type;
      newCustomField.required = createDto.required;
      newCustomField.configJson = createDto.configJson;
      newCustomField.userId = createDto.userId || null;
      newCustomField.employeeId = createDto.employeeId || null;
      newCustomField.status = CustomFieldStatus.PENDING;

      // Lưu vào database
      const savedCustomField = await this.customFieldRepository.save(newCustomField);

      // Nếu có formGroupId, tạo liên kết với nhóm
      if (formGroup) {
        const groupFormField = new CustomGroupFormField();
        groupFormField.fieldId = savedCustomField.id;
        groupFormField.formGroupId = (formGroup as CustomGroupForm).id;
        groupFormField.gird = createDto.grid || { i: `field-${savedCustomField.id}`, x: 0, y: 0, w: 6, h: 2 };
        groupFormField.value = createDto.value || { value: 'example' };

        await this.customGroupFormFieldRepository.save(groupFormField);
      }

      // Chuyển đổi sang DTO response
      // Tạo một đối tượng trung gian với các giá trị đã được xử lý
      const processedData = {
        ...savedCustomField
      };

      // Chuyển đổi đối tượng trung gian sang DTO response
      return plainToInstance(CustomFieldResponseDto, processedData, {
        excludeExtraneousValues: true
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tạo trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
        'Lỗi khi tạo trường tùy chỉnh',
      );
    }
  }

  /**
   * Lấy danh sách trường tùy chỉnh với các điều kiện lọc và phân trang
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách trường tùy chỉnh với phân trang
   */
  async findAll(queryDto: QueryCustomFieldDto): Promise<PaginatedResult<CustomFieldListItemDto>> {
    try {
      this.logger.log(`Lấy danh sách trường tùy chỉnh với điều kiện: ${JSON.stringify(queryDto)}`);

      // Lấy danh sách trường tùy chỉnh từ repository
      const result = await this.customFieldRepository.findAll(queryDto);
      this.logger.log(`Repository trả về ${result.items.length} items`);

      // Chuyển đổi sang DTO response
      const items = result.items.map(item => {
        return plainToInstance(CustomFieldListItemDto, item, { excludeExtraneousValues: true });
      });

      this.logger.log(`Đã chuyển đổi thành công ${items.length} items sang DTO`);

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách trường tùy chỉnh: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_FIND_FAILED,
        'Lỗi khi lấy danh sách trường tùy chỉnh',
      );
    }
  }

  /**
   * Lấy chi tiết trường tùy chỉnh theo ID
   * @param id ID của trường tùy chỉnh
   * @returns Thông tin chi tiết trường tùy chỉnh
   */
  async findById(id: number): Promise<CustomFieldDetailResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết trường tùy chỉnh với ID: ${id}`);

      // Lấy thông tin trường tùy chỉnh
      const customField = await this.customFieldRepository.findByIdAndNotDeleted(id);
      this.validationHelper.validateCustomFieldExists(customField, id);

      // Lấy danh sách nhóm liên kết
      const groupFormFields = await this.customGroupFormFieldRepository.find({
        where: { fieldId: id },
      });

      // Lấy thông tin chi tiết của các nhóm
      const linkedGroups: LinkedGroupDto[] = [];

      for (const groupFormField of groupFormFields) {
        const groupForm = await this.customGroupFormRepository.findById(groupFormField.formGroupId);
        if (groupForm) {
          linkedGroups.push({
            formGroupId: groupFormField.formGroupId,
            groupLabel: groupForm.label,
            grid: groupFormField.gird,
            value: groupFormField.value,
          });
        }
      }

      if (!customField) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy trường tùy chỉnh với ID ${id}`,
        );
      }

      const processedData = {
        ...customField,
        linkedGroups,
      };

      return plainToInstance(CustomFieldDetailResponseDto, processedData, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_FIND_FAILED,
        'Lỗi khi lấy chi tiết trường tùy chỉnh',
      );
    }
  }

  /**
   * Cập nhật trường tùy chỉnh
   * @param id ID của trường tùy chỉnh
   * @param updateDto DTO chứa thông tin cập nhật
   * @returns Thông tin trường tùy chỉnh đã cập nhật
   */
  @Transactional()
  async update(id: number, updateDto: UpdateCustomFieldDto): Promise<CustomFieldResponseDto> {
    try {
      this.logger.log(`Cập nhật trường tùy chỉnh với ID ${id}: ${JSON.stringify(updateDto)}`);

      // Kiểm tra trường tùy chỉnh tồn tại và không bị xóa
      const customField = await this.customFieldRepository.findByIdAndNotDeleted(id);
      this.validationHelper.validateCustomFieldExists(customField, id);

      // Kiểm tra tính hợp lệ của dữ liệu cập nhật
      this.validationHelper.validateUpdateCustomFieldData(updateDto, customField);

      // Cập nhật các trường được cung cấp
      if (updateDto.component) {
        customField.component = updateDto.component;
      }

      if (updateDto.label) {
        customField.label = updateDto.label;
      }

      if (updateDto.type) {
        customField.type = updateDto.type;
      }

      if (updateDto.required !== undefined) {
        customField.required = updateDto.required;
      }

      if (updateDto.configJson) {
        customField.configJson = updateDto.configJson;
      }

      // Lưu vào database
      const savedCustomField = await this.customFieldRepository.save(customField);

      // Chuyển đổi sang DTO response
      const processedData = {
        ...savedCustomField
      };

      return plainToInstance(CustomFieldResponseDto, processedData, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
        'Lỗi khi cập nhật trường tùy chỉnh',
      );
    }
  }

  /**
   * Xóa trường tùy chỉnh (soft delete)
   * @param id ID của trường tùy chỉnh
   * @param userId ID của người dùng hiện tại
   */
  @Transactional()
  async delete(id: number, userId: number): Promise<void> {
    try {
      this.logger.log(`Xóa trường tùy chỉnh với ID ${id} bởi người dùng ${userId}`);

      // Kiểm tra trường tùy chỉnh tồn tại và không bị xóa
      const customField = await this.customFieldRepository.findByIdAndNotDeleted(id);
      this.validationHelper.validateCustomFieldExists(customField, id);

      // Kiểm tra xem có phải là user hiện tại không thì mới được quyền xóa
      if (customField.userId !== userId) {
        this.logger.error(`Người dùng ${userId} không có quyền xóa trường tùy chỉnh ${id} của người dùng ${customField.userId}`);
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOM_FIELD_DELETE_FAILED,
          'Bạn không có quyền xóa trường tùy chỉnh này'
        );
      }

      // Cập nhật trạng thái thành DELETED
      customField.status = CustomFieldStatus.DELETED;
      await this.customFieldRepository.save(customField);

      // Xóa các bản ghi liên kết trong bảng custom_group_form_field
      const groupFormFields = await this.customGroupFormFieldRepository.find({
        where: { fieldId: id },
      });

      if (groupFormFields.length > 0) {
        await this.customGroupFormFieldRepository.remove(groupFormFields);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xóa trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_DELETE_FAILED,
        'Lỗi khi xóa trường tùy chỉnh',
      );
    }
  }

  /**
   * Lấy danh sách thành phần của admin và user
   * @param userId ID người dùng (tùy chọn)
   * @param employeeId ID nhân viên (tùy chọn)
   * @returns Danh sách thành phần
   */
  async getComponents(userId?: number, employeeId?: number): Promise<ComponentListResponseDto> {
    try {
      this.logger.log(`Lấy danh sách thành phần với userId=${userId}, employeeId=${employeeId}`);

      // Lấy danh sách thành phần mặc định
      const defaultComponents = [...DEFAULT_COMPONENTS];

      // Lấy danh sách trường tùy chỉnh của người dùng hoặc nhân viên
      const where: any = {
        status: EntityStatusEnum.PENDING,
      };

      if (userId) {
        where.userId = userId;
      }

      if (employeeId) {
        where.employeeId = employeeId;
      }

      const customFields = await this.customFieldRepository.find({ where });

      // Chuyển đổi trường tùy chỉnh thành định dạng thành phần
      const customComponents = customFields.map(field => {
        // Tạo cấu hình từ configJson
        const config = {
          id: field.configId,
          label: field.label,
          type: field.type,
          required: field.required,
          ...field.configJson,
        };

        // Nếu có validation trong configJson, đưa nó lên cấp cao nhất
        if (field.configJson && field.configJson.validation) {
          Object.assign(config, field.configJson.validation);
        }

        return {
          component: field.component,
          config,
        };
      });

      // Kết hợp thành phần mặc định và tùy chỉnh
      const allComponents = [...defaultComponents, ...customComponents];

      // Chuyển đổi sang DTO response
      const response = {
        data: allComponents,
        total: allComponents.length,
      };

      return plainToInstance(ComponentListResponseDto, response, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách thành phần: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_FIND_FAILED,
        'Lỗi khi lấy danh sách thành phần',
      );
    }
  }
}
