import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, FindOptionsWhere, ILike, Not, SelectQueryBuilder, In } from 'typeorm';
import { CustomField, CustomFieldStatus } from '@modules/business/entities';
import { PaginatedResult } from '@common/response/api-response-dto';
import { QueryCustomFieldDto as UserQueryCustomFieldDto } from '@modules/business/user/dto';
import { QueryCustomFieldDto as AdminQueryCustomFieldDto } from '@modules/business/admin/dto';
import { EntityStatusEnum } from '@modules/business/enums';

/**
 * Repository xử lý truy vấn dữ liệu cho entity CustomField
 */
@Injectable()
export class CustomFieldRepository extends Repository<CustomField> {
  protected readonly logger = new Logger(CustomFieldRepository.name);

  constructor(private dataSource: DataSource) {
    super(CustomField, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho CustomField
   * @returns SelectQueryBuilder<CustomField>
   */
  protected createBaseQuery(): SelectQueryBuilder<CustomField> {
    return this.createQueryBuilder('customField');
  }

  /**
   * Tìm trường tùy chỉnh theo ID
   * @param id ID của trường tùy chỉnh
   * @returns Trường tùy chỉnh hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<CustomField | null> {
    try {
      return await this.findOne({ where: { id } });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm trường tùy chỉnh theo ID ${id}: ${error.message}`);
      throw new Error(`Lỗi khi tìm trường tùy chỉnh theo ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm trường tùy chỉnh theo ID và ID người dùng
   * @param id ID của trường tùy chỉnh
   * @param userId ID của người dùng
   * @returns Trường tùy chỉnh hoặc null nếu không tìm thấy
   */
  async findByIdAndUserId(id: number, userId: number): Promise<CustomField | null> {
    try {
      return await this.findOne({ where: { id, userId } });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm trường tùy chỉnh theo ID ${id} và userId ${userId}: ${error.message}`);
      throw new Error(`Lỗi khi tìm trường tùy chỉnh theo ID ${id} và userId ${userId}: ${error.message}`);
    }
  }

  /**
   * Tìm kiếm trường tùy chỉnh với các điều kiện lọc và phân trang (User)
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách trường tùy chỉnh với phân trang
   */
  async findAll(queryDto: UserQueryCustomFieldDto): Promise<PaginatedResult<CustomField>> {
    try {
      this.logger.log(`Repository findAll được gọi với queryDto: ${JSON.stringify(queryDto)}`);

      const {
        page = 1,
        limit = 10,
        userId,
        employeeId,
        type,
        sortBy = 'createAt',
        sortDirection = 'DESC',
      } = queryDto;

      this.logger.log(`Parsed parameters: page=${page}, limit=${limit}, sortBy=${sortBy}, sortDirection=${sortDirection}`);

      // Tính toán offset từ page và limit
      const skip = (page - 1) * limit;

      // Validate sortBy field để tránh lỗi SQL injection và trường không tồn tại
      const validSortFields = ['id', 'component', 'configId', 'label', 'type', 'createAt', 'status'];
      const safeSortBy = validSortFields.includes(sortBy) ? sortBy : 'createAt';
      this.logger.log(`Safe sortBy: ${safeSortBy}`);

      // Tạo điều kiện lọc
      const where: FindOptionsWhere<CustomField> = {
        status: Not(CustomFieldStatus.DELETED),
      };

      // Thêm điều kiện lọc theo userId nếu có
      if (userId) {
        where.userId = userId;
      }

      // Thêm điều kiện lọc theo employeeId nếu có
      if (employeeId) {
        where.employeeId = employeeId;
      }

      // Thêm điều kiện lọc theo type nếu có
      if (type) {
        where.type = type;
      }

      this.logger.log(`Where conditions: ${JSON.stringify(where)}`);

      // Tính tổng số bản ghi
      const total = await this.count({ where });
      this.logger.log(`Total count: ${total}`);

      // Tạo order object an toàn
      const order: any = {};
      order[safeSortBy] = sortDirection;
      this.logger.log(`Order object: ${JSON.stringify(order)}`);

      // Lấy danh sách trường tùy chỉnh
      const items = await this.find({
        where,
        order,
        skip,
        take: limit,
      });

      this.logger.log(`Found ${items.length} items`);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm kiếm trường tùy chỉnh: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm kiếm trường tùy chỉnh: ${error.message}`);
    }
  }

  /**
   * Tìm trường tùy chỉnh theo ID và không bị xóa
   * @param id ID của trường tùy chỉnh
   * @returns Trường tùy chỉnh hoặc null nếu không tìm thấy
   */
  async findByIdAndNotDeleted(id: number): Promise<CustomField | null> {
    try {
      return await this.findOne({
        where: {
          id,
          status: Not(CustomFieldStatus.DELETED),
        },
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm trường tùy chỉnh theo ID ${id} và không bị xóa: ${error.message}`);
      throw new Error(`Lỗi khi tìm trường tùy chỉnh theo ID ${id} và không bị xóa: ${error.message}`);
    }
  }

  /**
   * Tìm kiếm trường tùy chỉnh với các điều kiện lọc (Admin)
   * @param queryParams Tham số truy vấn
   * @returns Danh sách trường tùy chỉnh phân trang
   */
  async findCustomFields(queryParams: AdminQueryCustomFieldDto): Promise<PaginatedResult<CustomField>> {
    this.logger.log(`Tìm kiếm trường tùy chỉnh với các tham số: ${JSON.stringify(queryParams)}`);

    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createAt',
      sortDirection = 'DESC',
      type,
      component,
      employeeId,
      status
    } = queryParams;

    const skip = (page - 1) * limit;

    const query = this.createBaseQuery();
    this.logger.log(`Đã tạo query builder cơ bản`);

    // Thêm điều kiện tìm kiếm
    if (search) {
      this.logger.log(`Thêm điều kiện tìm kiếm với từ khóa: ${search}`);
      query.andWhere('(customField.label ILIKE :search OR customField.configId ILIKE :search)', {
        search: `%${search}%`,
      });
    }

    // Lọc theo loại trường
    if (type) {
      this.logger.log(`Lọc theo loại trường: ${type}`);
      query.andWhere('customField.type = :type', { type });
    }

    // Lọc theo thành phần UI
    if (component) {
      this.logger.log(`Lọc theo thành phần UI: ${component}`);
      query.andWhere('customField.component = :component', { component });
    }

    // Lọc theo người tạo (admin hoặc user)
    if (employeeId) {
      this.logger.log(`Lọc theo nhân viên tạo: ${employeeId}`);
      query.andWhere('customField.employeeId = :employeeId', { employeeId });
    }
    // Lấy tất cả trường tùy chỉnh, bất kể là của user hay employee
    this.logger.log(`Lấy tất cả trường tùy chỉnh, bất kể là của user hay employee`);

    // Lọc theo trạng thái
    if (status) {
      this.logger.log(`Lọc theo trạng thái: ${status}`);
      query.andWhere('customField.status = :status', { status });
    } else {
      // Nếu không có status, loại trừ các trường có status là DELETED
      this.logger.log(`Loại trừ các trường có status là DELETED`);
      query.andWhere('customField.status != :deletedStatus', { deletedStatus: CustomFieldStatus.DELETED });
    }

    // Thêm sắp xếp
    this.logger.log(`Sắp xếp theo ${sortBy} ${sortDirection}`);
    // Xử lý trường hợp đặc biệt khi client gửi 'createdAt' thay vì 'createAt'
    const sortColumn = sortBy === 'createdAt' ? 'createAt' : sortBy;
    this.logger.log(`Áp dụng sắp xếp theo cột ${sortColumn}`);
    query.orderBy(`customField.${sortColumn}`, sortDirection);

    // Thêm phân trang
    this.logger.log(`Phân trang: skip ${skip}, limit ${limit}`);
    query.skip(skip).take(limit);

    // Thực hiện truy vấn
    this.logger.log(`Đang thực hiện truy vấn`);
    const [items, total] = await query.getManyAndCount();
    this.logger.log(`Đã tìm thấy ${items.length}/${total} trường tùy chỉnh`);

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm trường tùy chỉnh theo ID (Admin)
   * @param id ID trường tùy chỉnh
   * @returns Trường tùy chỉnh hoặc null
   */
  async findCustomFieldById(id: number): Promise<CustomField | null> {
    return this.createBaseQuery()
      .where('customField.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm trường tùy chỉnh theo configId (Admin)
   * @param configId ConfigId của trường tùy chỉnh
   * @returns Trường tùy chỉnh hoặc null
   */
  async findCustomFieldByConfigId(configId: string): Promise<CustomField | null> {
    return this.createBaseQuery()
      .where('customField.configId = :configId', { configId })
      .getOne();
  }

  /**
   * Tìm các trường tùy chỉnh theo danh sách ID
   * @param ids Danh sách ID của các trường tùy chỉnh
   * @returns Danh sách trường tùy chỉnh
   */
  async findByIds(ids: number[]): Promise<CustomField[]> {
    if (ids.length === 0) {
      return [];
    }

    return this.find({
      where: { id: In(ids) }
    });
  }
}