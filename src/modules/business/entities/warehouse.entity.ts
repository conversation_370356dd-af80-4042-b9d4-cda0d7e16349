import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { WarehouseTypeEnum } from '@modules/business/enums';

/**
 * Entity đại diện cho bảng warehouse trong cơ sở dữ liệu
 * Bảng quản lý kho
 */
@Entity('warehouse')
export class Warehouse {
  /**
   * ID của kho
   */
  @PrimaryGeneratedColumn({ name: 'warehouse_id' })
  warehouseId: number;

  /**
   * Tên kho
   */
  @Column({ name: 'name', length: 100, nullable: false, comment: 'Tên kho' })
  name: string;

  /**
   * <PERSON>ô tả kho
   */
  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
    comment: 'Mô tả kho',
  })
  description: string;

  /**
   * Loại kho
   */
  @Column({
    name: 'type',
    type: 'enum',
    enum: WarehouseTypeEnum,
    default: WarehouseTypeEnum.PHYSICAL,
    nullable: false,
    comment: '<PERSON>ại kho',
  })
  type: WarehouseTypeEnum;
}
