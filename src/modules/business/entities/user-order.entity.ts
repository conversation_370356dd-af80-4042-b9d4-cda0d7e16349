import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { User } from '@modules/user/entities/user.entity';
import { UserConvertCustomer } from './user-convert-customer.entity';
import { IsOptional } from 'class-validator';

/**
 * Entity đại diện cho bảng user_orders trong cơ sở dữ liệu
 * Bảng quản lý đơn hàng của người dùng
 */
@Entity('user_orders')
export class UserOrder {
  /**
   * ID của đơn hàng
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của khách hàng
   */
  @Column({ name: 'user_convert_customer_id', type: 'bigint', nullable: true, comment: 'Khách hàng đặt đơn' })
  userConvertCustomerId: number;

  /**
   * ID của người dùng sở hữu đơn hàng
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true, comment: 'Người dùng sở hữu đơn hàng' })
  userId: number;

  /**
   * Thông tin sản phẩm
   */
  @Column({ name: 'product_info', type: 'jsonb', nullable: true, comment: 'Thông tin sản phẩm (JSON)' })
  productInfo: Record<string, unknown>;

  /**
   * Thông tin hóa đơn
   */
  @Column({ name: 'bill_info', type: 'jsonb', nullable: true, comment: 'Thông tin hóa đơn (JSON)' })
  billInfo: Record<string, unknown>;

  /**
   * Đơn hàng có yêu cầu vận chuyển hay không
   */
  @Column({ name: 'has_shipping', type: 'boolean', default: true, nullable: false, comment: 'Đơn hàng có yêu cầu vận chuyển hay không' })
  hasShipping: boolean;

  /**
   * Trạng thái vận chuyển
   */
  @Column({ name: 'shipping_status', length: 50, nullable: true, comment: 'Trạng thái vận chuyển (ví dụ: pending, shipped, delivered)' })
  shippingStatus: string;

  /**
   * Thông tin vận chuyển chi tiết
   */
  @Column({ name: 'logistic_info', type: 'jsonb', nullable: true, comment: 'Thông tin vận chuyển chi tiết (JSON)' })
  logisticInfo: Record<string, unknown>;

  /**
   * Thời gian tạo đơn hàng
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false, default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint", comment: 'Thời gian tạo đơn hàng' })
  createdAt: number;

  /**
   * Thời gian cập nhật đơn hàng
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false, default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint", comment: 'Thời gian cập nhật đơn hàng' })
  updatedAt: number;

  /**
   * Nguồn đơn hàng
   */
  @Column({ name: 'source', length: 45, nullable: true, comment: 'Nguồn đơn hàng' })
  source: string;
}
