import { AppException } from '@/common/exceptions';
import { PaginatedResult } from '@/common/response';
import { Injectable, Logger } from '@nestjs/common';
import { MODEL_TRAINING_ERROR_CODES } from '../../exceptions';
import { ApiKeyEncryptionHelper } from '../../helpers/api-key-encryption.helper';
import { BaseModelRepository } from '../../repositories';
import { UserProviderModelRepository } from '../../repositories/user-provider-model.repository';
import { CdnService } from '@/shared/services/cdn.service';
import { TypeProviderEnum } from '../../constants/type-provider.enum';
import { BaseModelQueryDto } from '../../dto/base-model/base-model-query.dto';
import { BaseModelUserResponseDto } from '../../dto/base-model/base-model-user-response.dto';
import { BaseModel } from '../../entities/base-model.entity';
import { ModelResponse } from '../../interfaces/model-response.interface';
import { BaseModelUserMapper } from '../../mappers/base-model-user.mapper';
import { AnthropicService } from '@/shared/services/ai/anthropic.service';
import { DeepSeekService } from '@/shared/services/ai/deepseek.service';
import { GoogleAIService } from '@/shared/services/ai/google_ai.service';
import { MetaAIService } from '@/shared/services/ai/metaai.service';
import { OpenAiService } from '@/shared/services/ai/openai.service';
import { XAIService } from '@/shared/services/ai/xai.service';

@Injectable()
export class BaseModelUserService {
  private readonly logger = new Logger(BaseModelUserService.name);

  constructor(
    private readonly baseModelRepository: BaseModelRepository,
    private readonly cdnService: CdnService,
    private readonly userProviderModelRepository: UserProviderModelRepository,
    private readonly apiKeyEncryptionHelper: ApiKeyEncryptionHelper,
    private readonly openAiService: OpenAiService,
    private readonly anthropicService: AnthropicService,
    private readonly googleAIService: GoogleAIService,
    private readonly metaAIService: MetaAIService,
    private readonly xAIService: XAIService,
    private readonly deepSeekService: DeepSeekService,
  ) { }

  /**
   * Lấy danh sách model từ provider của user
   * @param providerId ID của provider
   * @param userId ID của user
   * @returns Danh sách model từ provider đã chuyển đổi sang BaseModelUserResponseDto
   */
  async getModelsFromUserProvider(
    providerId: string,
    userId: number
  ): Promise<BaseModelUserResponseDto[]> { // Trả về mảng các đối tượng BaseModelUserResponseDto đã được chuyển đổi từ model của provider
    try {
      // Lấy thông tin provider
      const provider = await this.userProviderModelRepository.findByIdAndUserId(providerId, userId);

      if (!provider) {
        this.logger.error(`Provider với ID ${providerId} không tồn tại hoặc không thuộc về user ${userId}`);
        throw new AppException(MODEL_TRAINING_ERROR_CODES.USER_PROVIDER_NOT_FOUND);
      }

      // Lấy tất cả model base của hệ thống theo provider type
      // Đây là bước quan trọng - lấy model base làm gốc
      // Chỉ lấy các model có trạng thái APPROVED
      const baseModels = await this.baseModelRepository.findAllByProviderType(provider.type);
      this.logger.debug(`Đã tìm thấy ${baseModels.length} model base của hệ thống cho provider type ${provider.type}`);

      if (baseModels.length === 0) {
        this.logger.warn(`Không tìm thấy model base nào cho provider type ${provider.type} ở trạng thái APPROVED`);
        return [];
      }

      // Giải mã API key
      const apiKeyDecode = this.apiKeyEncryptionHelper.decryptUserApiKey(provider.apiKey, userId);

      // Lấy danh sách model từ provider tương ứng
      let providerModels: ModelResponse;

      switch (provider.type) {
        case TypeProviderEnum.OPENAI:
          providerModels = await this.openAiService.listModels(apiKeyDecode);
          break;
        case TypeProviderEnum.ANTHROPIC:
          providerModels = await this.anthropicService.getModels(apiKeyDecode);
          break;
        case TypeProviderEnum.GOOGLE:
          providerModels = await this.googleAIService.getModels(apiKeyDecode);
          break;
        case TypeProviderEnum.META:
          providerModels = await this.metaAIService.getModels(apiKeyDecode);
          break;
        case TypeProviderEnum.XAI:
          providerModels = await this.xAIService.getModels(apiKeyDecode);
          break;
        case TypeProviderEnum.DEEPSEEK:
          providerModels = await this.deepSeekService.getModels(apiKeyDecode);
          break;
        default:
          throw new AppException(
            MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR,
            `Loại provider ${provider.type} không được hỗ trợ`
          );
      }

      // Chuyển đổi kết quả sang định dạng BaseModelUserResponseDto
      const result: BaseModelUserResponseDto[] = [];

      if (Array.isArray(providerModels)) {
        // Tạo map từ ID của model base để tìm kiếm nhanh hơn
        const baseModelMap = new Map<string, BaseModel>();
        for (const baseModel of baseModels) {
          baseModelMap.set(baseModel.id.toLowerCase(), baseModel);
        }

        for (const providerModel of providerModels) {
          const dto = new BaseModelUserResponseDto();

          // Lấy ID và tên của model tùy theo loại provider
          let providerModelId = '';
          if ('id' in providerModel) {
            providerModelId = providerModel.id;
            dto.id = providerModelId;
            dto.model_id = providerModelId; // Mặc định dùng ID làm model_id
          }

          // Nếu có trường name thì sử dụng cho model_id
          if ('name' in providerModel && providerModel.name) {
            dto.model_id = providerModel.name;
          }

          // Nếu có trường description thì sử dụng
          if ('description' in providerModel && providerModel.description) {
            dto.description = providerModel.description;
          }

          // Thêm thông tin provider
          dto.providerType = provider.type;

          // Tìm model base của hệ thống có ID tương đồng nhất
          // Lưu ý: Chúng ta lấy model base làm gốc, và map model provider vào model base
          const matchedBaseModel = this.findBestMatchingBaseModel(providerModelId, provider.type, baseModels);

          // Nếu tìm thấy model base phù hợp, sử dụng config của nó
          if (matchedBaseModel && matchedBaseModel.config) {
            dto.config = matchedBaseModel.config;
            dto.baseModelId = matchedBaseModel.id; // Lưu ID của model base để tham chiếu
            this.logger.debug(`Đã map model provider ${providerModelId} với model base ${matchedBaseModel.id}`);
          } else {
            // Nếu không tìm thấy, sử dụng config mặc định
            dto.config = {
              hasTopP: true,
              hasTopK: true,
              hasFunction: true,
              hasTemperature: true,
              hasText: true,
              hasImage: false,
              hasAudio: false,
              hasVideo: false,
              hasParallelToolCall: false,
              hasReasoningEffort: [],
              hasMaxTokens: true,
              hasResponseFormat: ['text'],
              hasFileSearch: false,
              hasCodeInterpreter: false,
              hasStreaming: true,
              hasSystemMessage: true,
              hasStopSequences: false,
              hasPresencePenalty: false,
              hasFrequencyPenalty: false,
              hasLogitBias: false,
              hasSeed: false,
            };
            this.logger.debug(`Không tìm thấy model base phù hợp cho model provider ${providerModelId}, sử dụng config mặc định`);
          }

          // Thêm thời gian tạo
          dto.createdAt = Date.now();

          result.push(dto);
        }
      }

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy model từ provider: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm model base của hệ thống có ID tương đồng nhất với ID của model provider
   * @param providerModelId ID của model từ provider
   * @param providerType Loại provider (không sử dụng trong phiên bản hiện tại nhưng giữ lại để mở rộng trong tương lai)
   * @param baseModels Danh sách model base của hệ thống
   * @returns Model base có ID tương đồng nhất hoặc null nếu không tìm thấy
   */
  private findBestMatchingBaseModel(
    providerModelId: string,
    _providerType: TypeProviderEnum, // Thêm dấu gạch dưới để chỉ ra rằng tham số này không được sử dụng
    baseModels: BaseModel[]
  ): BaseModel | null {
    if (!baseModels || baseModels.length === 0) {
      return null;
    }

    // Chuyển ID về chữ thường để so sánh không phân biệt hoa thường
    const normalizedId = providerModelId.toLowerCase();

    // Tìm model có ID giống hệt
    const exactMatch = baseModels.find(model => model.id.toLowerCase() === normalizedId);
    if (exactMatch) {
      return exactMatch;
    }

    // Xử lý đặc biệt cho Claude-3
    if (normalizedId.startsWith('claude-3')) {
      // Lấy cả 3 phần: claude-3-opus, claude-3-sonnet, claude-3-haiku
      const parts = normalizedId.split('-');
      if (parts.length >= 3) {
        const specificModel = `${parts[0]}-${parts[1]}-${parts[2]}`;
        const specificMatch = baseModels.find(model =>
          model.id.toLowerCase().includes(specificModel)
        );
        if (specificMatch) {
          return specificMatch;
        }
      }
    }

    // Xử lý cho các model có số phiên bản (ví dụ: gpt-3.5, gemini-1.5)
    const versionMatch = normalizedId.match(/^([a-z]+-\d+\.\d+)/);
    if (versionMatch) {
      const versionPrefix = versionMatch[1];
      const versionPrefixMatch = baseModels.find(model =>
        model.id.toLowerCase().startsWith(versionPrefix)
      );
      if (versionPrefixMatch) {
        return versionPrefixMatch;
      }
    }

    // Trích xuất phần chính của ID (ví dụ: gpt-4 từ gpt-4-turbo)
    const parts = normalizedId.split('-');
    const mainPart = parts.length >= 2 ? `${parts[0]}-${parts[1]}` : normalizedId;

    // Tìm model có ID chứa phần chính
    const mainPartMatch = baseModels.find(model =>
      model.id.toLowerCase().includes(mainPart)
    );

    if (mainPartMatch) {
      return mainPartMatch;
    }

    // Tìm model có ID là tiền tố của providerModelId
    for (const baseModel of baseModels) {
      const baseModelId = baseModel.id.toLowerCase();
      if (normalizedId.startsWith(baseModelId)) {
        return baseModel;
      }
    }

    // Tìm model có ID mà providerModelId là tiền tố
    for (const baseModel of baseModels) {
      const baseModelId = baseModel.id.toLowerCase();
      if (baseModelId.startsWith(normalizedId)) {
        return baseModel;
      }
    }

    // Nếu không tìm thấy, trả về null
    return null;
  }

  /**
   * Finds all base models with pagination and search
   * Chỉ trả về các model có trạng thái APPROVED từ hệ thống
   * @param query Query parameters for pagination and search
   * @returns Paginated list of base models
   */
  async findAllByUser(
    query: BaseModelQueryDto,
  ): Promise<PaginatedResult<BaseModelUserResponseDto>> { // Trả về kết quả phân trang chứa các model base từ hệ thống
    // Lấy model từ hệ thống
    // Không cần xác thực status vì repository đã lọc theo APPROVED
    const result = await this.baseModelRepository.findAllByUser(query);

    // Chuyển đổi sang DTO mới không chứa các trường không cần thiết
    const updatedItems = await BaseModelUserMapper.toDtoList(
      result.items,
      this.baseModelRepository
    );

    return {
      items: updatedItems,
      meta: result.meta,
    }
  }

  /**
   * Lấy tất cả model từ provider của user không phân trang
   * @param providerId ID của provider
   * @param userId ID của user
   * @returns Danh sách tất cả model từ provider của user
   */
  async findAllByUserProvider(
    providerId: string,
    userId: number,
  ): Promise<BaseModelUserResponseDto[]> { // Trả về danh sách tất cả model từ provider của user
    try {
      // Lấy danh sách model từ provider và chuyển đổi sang BaseModelUserResponseDto
      const models = await this.getModelsFromUserProvider(providerId, userId);

      // Trả về tất cả model không phân trang
      return models;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy model từ provider: ${error.message}`, error.stack);
      throw error;
    }
  }
}
