import { BaseModel } from '@modules/model-training/entities';
import { IsString, IsOptional, IsNumber, IsObject, IsEnum, Min, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseModelStatusEnum } from "@/modules/model-training/constants/base-model-status.enum";
import { ConfigModelBase } from '../../interfaces/config-model-base.interface';
import { Type } from 'class-transformer';

/**
 * DTO để nhận dữ liệu từ request khi tạo base model
 */
export class CreateBaseModelDto {
  /**
   * Tên hiển thị của mô hình (sẽ được lưu vào trường modelId trong entity)
   */
  @ApiProperty({
    description: 'Tên hiển thị của mô hình (sẽ được lưu vào trường modelId trong entity)',
    example: 'GPT-4O Mini Audio Preview',
  })
  @IsString()
  @IsOptional()
  name?: string;

  /**
   * <PERSON><PERSON> tả chi tiết về mô hình
   */
  @ApiProperty({
    description: 'Mô tả chi tiết về mô hình',
    example: 'Mô hình này sử dụng cho preview âm thanh với khả năng xử lý ngữ cảnh.',
    nullable: true,
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * ID của provider (UUID)
   */
  @ApiProperty({
    description: 'ID của provider mô hình (UUID)',
    example: '123e4567-e89b-12d3-a456-************',  // Ví dụ về ID nhà cung cấp (provider)
  })
  @IsString()
  providerId: string;

  /**
   * Tỷ lệ tính phí cho đầu vào khi sử dụng mô hình cơ bản
   */
  @ApiProperty({
    description: 'Tỷ lệ tính phí cho đầu vào khi sử dụng mô hình cơ bản',
    example: 5,  // Ví dụ về tỷ lệ đầu vào (dùng số nguyên theo SQL)
  })
  @IsNumber()
  @Min(1)
  baseInputRate: number;

  /**
   * Tỷ lệ tính phí cho đầu ra khi sử dụng mô hình cơ bản
   */
  @ApiProperty({
    description: 'Tỷ lệ tính phí cho đầu ra khi sử dụng mô hình cơ bản',
    example: 8,  // Ví dụ về tỷ lệ đầu ra (dùng số nguyên theo SQL)
  })
  @IsNumber()
  @Min(1)
  baseOutputRate: number;

  /**
   * Tỷ lệ tính phí cho huấn luyện với mô hình cơ bản
   */
  @ApiProperty({
    description: 'Tỷ lệ tính phí cho huấn luyện với mô hình cơ bản',
    example: 10,  // Ví dụ về tỷ lệ huấn luyện (dùng số nguyên theo SQL)
  })
  @IsNumber()
  @Min(1)
  baseTrainRate: number;

  /**
   * Tỷ lệ tính phí cho đầu vào khi sử dụng mô hình fine-tuning
   */
  @ApiProperty({
    description: 'Tỷ lệ tính phí cho đầu vào khi sử dụng mô hình fine-tuning',
    example: 7,  // Ví dụ về tỷ lệ fine-tuning đầu vào (dùng số nguyên theo SQL)
  })
  @IsNumber()
  @Min(1)
  fineTuningInputRate: number;

  /**
   * Tỷ lệ tính phí cho đầu ra khi sử dụng mô hình fine-tuning
   */
  @ApiProperty({
    description: 'Tỷ lệ tính phí cho đầu ra khi sử dụng mô hình fine-tuning',
    example: 9,  // Ví dụ về tỷ lệ fine-tuning đầu ra (dùng số nguyên theo SQL)
  })
  @IsNumber()
  @Min(1)
  fineTuningOutputRate: number;

  /**
   * Tỷ lệ tính phí cho huấn luyện với mô hình fine-tuning
   */
  @ApiProperty({
    description: 'Tỷ lệ tính phí cho huấn luyện với mô hình fine-tuning',
    example: 15,  // Ví dụ về tỷ lệ fine-tuning huấn luyện (dùng số nguyên theo SQL)
  })
  @IsNumber()
  @Min(1)
  fineTuningTrainRate: number;

  /**
   * Số token tối đa mô hình có thể xử lý trong một lần request
   */
  @ApiProperty({
    description: 'Số token tối đa mô hình có thể xử lý trong một lần request',
    example: 50000,  // Ví dụ về số token tối đa (số nguyên theo SQL)
  })
  @IsNumber()
  @Min(1)
  tokenCount: number;

  @ApiProperty({
    description: 'Trạng thái của base model',
    example: BaseModelStatusEnum.DRAFT,
    enum: BaseModelStatusEnum,
  })
  @IsEnum(BaseModelStatusEnum)
  status: BaseModelStatusEnum;

  /**
   * Cấu hình của mô hình dưới dạng JSON
   */
  @ApiProperty({
    description: 'Cấu hình của mô hình dưới dạng JSON',
    example: {
      hasTopP: true,
      hasTopK: true,
      hasFunction: true,
      hasTemperature: true,
      hasText: true,
      hasImage: true,
      hasAudio: true,
      hasVideo: true,
      hasParallelToolCall: true,
      hasReasoningEffort: ['low', 'medium', 'high'],
      hasMaxTokens: true,
      hasResponseFormat: ['text', 'json_object', 'json_schema'],
      hasFileSearch: true,
      hasCodeInterpreter: true,
      hasStreaming: true,
      hasSystemMessage: true,
      hasStopSequences: true,
      hasPresencePenalty: true,
      hasFrequencyPenalty: true,
      hasLogitBias: false,
      hasSeed: true,
    },
  })
  @IsObject()
  config: ConfigModelBase;
}

export class UpdateBaseModelDto {
  @ApiPropertyOptional({ description: 'Mô tả chi tiết mô hình' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Tỷ lệ phí input base' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  baseInputRate?: number;

  @ApiPropertyOptional({ description: 'Tỷ lệ phí output base' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  baseOutputRate?: number;

  @ApiPropertyOptional({ description: 'Tỷ lệ phí train base' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  baseTrainRate?: number;

  @ApiPropertyOptional({ description: 'Tỷ lệ phí input fine-tuning' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  fineTuningInputRate?: number;

  @ApiPropertyOptional({ description: 'Tỷ lệ phí output fine-tuning' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  fineTuningOutputRate?: number;

  @ApiPropertyOptional({ description: 'Tỷ lệ phí train fine-tuning' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  fineTuningTrainRate?: number;

  @ApiPropertyOptional({ description: 'Số token tối đa' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  tokenCount?: number;


  /**
   * Cấu hình của mô hình dưới dạng JSON
   */
  @ApiPropertyOptional({
    description: 'Cấu hình của mô hình dưới dạng JSON',
    example: {
      hasTopP: true,
      hasTopK: true,
      hasFunction: true,
      hasTemperature: true,
      hasText: true,
      hasImage: true,
      hasAudio: true,
      hasVideo: true,
      hasParallelToolCall: true,
      hasReasoningEffort: ['low', 'medium', 'high'],
      hasMaxTokens: true,
      hasResponseFormat: ['text', 'json_object', 'json_schema'],
      hasFileSearch: true,
      hasCodeInterpreter: true,
      hasStreaming: true,
      hasSystemMessage: true,
      hasStopSequences: true,
      hasPresencePenalty: true,
      hasFrequencyPenalty: true,
      hasLogitBias: false,
      hasSeed: true,
    },
  })
  @IsOptional()
  @IsObject()
  config?: ConfigModelBase;

  @ApiProperty({
    description: 'Trạng thái của base model',
    example: BaseModelStatusEnum.DRAFT,
  })
  @IsEnum(BaseModelStatusEnum)
  status?: BaseModelStatusEnum;

}

/**
 * DTO cho thông tin người dùng (creator/editor)
 */
export class UserInfoDto {
  /**
   * ID của người dùng
   */
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  @IsNumber()
  id: number;

  /**
   * Tên đầy đủ của người dùng
   */
  @ApiProperty({
    description: 'Tên đầy đủ của người dùng',
    example: 'Nguyễn Văn A',
  })
  @IsString()
  name: string;

  /**
   * URL avatar của người dùng
   */
  @ApiProperty({
    description: 'URL avatar của người dùng',
    example: 'https://example.com/avatar.png',
    required: false,
  })
  @IsOptional()
  @IsString()
  avatar?: string;
}

export class BaseModelRes {
  /**
   * ID của mô hình (thường là ID từ OpenAI hoặc hệ thống khác)
   */
  @ApiProperty({
    description: 'ID của mô hình',
    example: 'gpt-4o-mini-audio-preview',
  })
  @IsString()
  id: string;

  /**
   * Model ID của mô hình (tên hiển thị của mô hình)
   */
  @ApiProperty({
    description: 'Model ID của mô hình',
    example: 'GPT-4O Mini Audio Preview',
    required: false,
  })
  @IsOptional()
  @IsString()
  modelId?: string;

  /**
   * Mô tả chi tiết về mô hình (có thể để trống)
   */
  @ApiProperty({
    description: 'Mô tả chi tiết về mô hình',
    example: 'Mô hình này sử dụng cho preview âm thanh với khả năng xử lý ngữ cảnh.',
    nullable: true,
    required: false,
  })
  @IsOptional()
  @IsString()
  description: string | null;

  /**
   * Tên của nhà cung cấp mô hình (ví dụ: 'OpenAI', 'Anthropic', ...)
   */
  @ApiProperty({
    description: 'Tên của nhà cung cấp mô hình',
    example: 'OpenAI',
    required: false,
  })
  @IsOptional()
  @IsString()
  providerName?: string;

  /**
   * ID của provider (UUID)
   */
  @ApiProperty({
    description: 'ID của provider mô hình (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsString()
  providerId?: string;

  /**
   * Trạng thái hiện tại của mô hình
   */
  @ApiProperty({
    description: 'Trạng thái của base model',
    example: BaseModelStatusEnum.DRAFT,
    enum: BaseModelStatusEnum,
  })
  @IsOptional()
  @IsEnum(BaseModelStatusEnum)
  status?: BaseModelStatusEnum;

  /**
   * Thời điểm tạo mô hình (timestamp)
   */
  @ApiProperty({
    description: 'Thời điểm tạo mô hình (đơn vị: milliseconds)',
    example: 1715523521893,
    required: false,
  })
  @IsOptional()
  createAt?: number;

  /**
   * Thời điểm cập nhật gần nhất (timestamp)
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật gần nhất của mô hình (đơn vị: milliseconds)',
    example: 1715529999000,
    required: false,
  })
  @IsOptional()
  updateAt?: number;

  /**
   * Cấu hình của mô hình dưới dạng JSON
   */
  @ApiProperty({
    description: 'Cấu hình của mô hình dưới dạng JSON',
    example: {
      hasTopP: true,
      hasTopK: true,
      hasFunction: true,
      hasTemperature: true,
      hasText: true,
      hasImage: true,
      hasAudio: true,
      hasVideo: true,
      hasParallelToolCall: true,
      hasReasoningEffort: ['low', 'medium', 'high'],
      hasMaxTokens: true,
      hasResponseFormat: ['text', 'json_object', 'json_schema'],
      hasFileSearch: true,
      hasCodeInterpreter: true,
      hasStreaming: true,
      hasSystemMessage: true,
      hasStopSequences: true,
      hasPresencePenalty: true,
      hasFrequencyPenalty: true,
      hasLogitBias: false,
      hasSeed: true,
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  config?: ConfigModelBase;

  /**
   * Thời điểm xóa mô hình (timestamp)
   */
  @ApiProperty({
    description: 'Thời điểm xóa mô hình (đơn vị: milliseconds)',
    example: 1715529999000,
    required: false,
    nullable: true,
  })
  @IsOptional()
  deletedAt?: number | null;

  /**
   * ID của người xóa mô hình
   */
  @ApiProperty({
    description: 'ID của người xóa mô hình',
    example: 1,
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  deletedBy?: number | null;
}

/**
 * DTO cho danh sách model đã xóa
 */
export class DeletedBaseModelRes {
  /**
   * ID của mô hình
   */
  @ApiProperty({
    description: 'ID của mô hình',
    example: 'gpt-4o-mini-audio-preview',
  })
  @IsString()
  id: string;

  /**
   * Mô tả chi tiết về mô hình (có thể để trống)
   */
  @ApiProperty({
    description: 'Mô tả chi tiết về mô hình',
    example: 'Mô hình này sử dụng cho preview âm thanh với khả năng xử lý ngữ cảnh.',
    nullable: true,
    required: false,
  })
  @IsOptional()
  @IsString()
  description: string | null;

  /**
   * Tên của nhà cung cấp mô hình
   */
  @ApiProperty({
    description: 'Tên của nhà cung cấp mô hình',
    example: 'OpenAI',
    required: false,
  })
  @IsOptional()
  @IsString()
  providerName?: string;

  /**
   * ID của provider (UUID)
   */
  @ApiProperty({
    description: 'ID của provider mô hình (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsString()
  providerId?: string;

  /**
   * Trạng thái hiện tại của mô hình
   */
  @ApiProperty({
    description: 'Trạng thái của base model',
    example: BaseModelStatusEnum.DELETED,
    enum: BaseModelStatusEnum,
  })
  @IsOptional()
  @IsEnum(BaseModelStatusEnum)
  status?: BaseModelStatusEnum;

  /**
   * Cấu hình của mô hình dưới dạng JSON
   */
  @ApiProperty({
    description: 'Cấu hình của mô hình dưới dạng JSON',
    example: {
      hasTopP: true,
      hasTopK: true,
      hasFunction: true,
      hasTemperature: true,
      hasText: true,
      hasImage: true,
      hasAudio: true,
      hasVideo: true,
      hasParallelToolCall: true,
      hasReasoningEffort: ['low', 'medium', 'high'],
      hasMaxTokens: true,
      hasResponseFormat: ['text', 'json_object', 'json_schema'],
      hasFileSearch: true,
      hasCodeInterpreter: true,
      hasStreaming: true,
      hasSystemMessage: true,
      hasStopSequences: true,
      hasPresencePenalty: true,
      hasFrequencyPenalty: true,
      hasLogitBias: false,
      hasSeed: true,
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  config?: ConfigModelBase;

  /**
   * Thời điểm xóa mô hình (timestamp)
   */
  @ApiProperty({
    description: 'Thời điểm xóa mô hình (đơn vị: milliseconds)',
    example: 1715529999000,
    required: false,
  })
  @IsOptional()
  deletedAt?: number;

  /**
   * Thông tin người xóa
   */
  @ApiProperty({
    description: 'Thông tin người xóa',
    type: UserInfoDto,
    required: false,
  })
  @IsOptional()
  @Type(() => UserInfoDto)
  @ValidateNested()
  deleter?: UserInfoDto;
}

/**
 * DTO chi tiết cho base model, mở rộng từ BaseModelRes
 */
export class BaseModelDetailRes extends BaseModelRes {
  /**
   * Thông tin người tạo
   */
  @ApiProperty({
    description: 'Thông tin người tạo',
    type: UserInfoDto,
  })
  @IsOptional()
  @Type(() => UserInfoDto)
  @ValidateNested()
  creator?: UserInfoDto;

  /**
   * Thông tin người cập nhật gần nhất
   */
  @ApiProperty({
    description: 'Thông tin người cập nhật gần nhất',
    type: UserInfoDto,
    required: false,
  })
  @IsOptional()
  @Type(() => UserInfoDto)
  @ValidateNested()
  editor?: UserInfoDto;

  /**
   * Thông tin người xóa
   */
  @ApiProperty({
    description: 'Thông tin người xóa',
    type: UserInfoDto,
    required: false,
  })
  @IsOptional()
  @Type(() => UserInfoDto)
  @ValidateNested()
  deleter?: UserInfoDto;
}
