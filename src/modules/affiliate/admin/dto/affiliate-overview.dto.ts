import { ApiProperty } from '@nestjs/swagger';
import { IsNumber } from 'class-validator';

/**
 * DTO cho thông tin tổng quan về affiliate
 */
export class AffiliateOverviewDto {
  @ApiProperty({
    description: 'Tổng số tài khoản affiliate (publishers)',
    example: 150,
  })
  @IsNumber()
  totalPublishers: number;

  @ApiProperty({
    description: 'Tổng số cấp bậc (affiliate ranks)',
    example: 5,
  })
  @IsNumber()
  totalRanks: number;

  @ApiProperty({
    description: 'Tổng số đơn hàng từ affiliate',
    example: 1200,
  })
  @IsNumber()
  totalOrders: number;

  @ApiProperty({
    description: 'Tổng số lần chuyển đổi điểm',
    example: 300,
  })
  @IsNumber()
  totalPointConversions: number;

  @ApiProperty({
    description: 'Tổng số tài khoản affiliate',
    example: 150,
  })
  @IsNumber()
  totalAccounts: number;

  @ApiProperty({
    description: 'Số tài khoản affiliate đang hoạt động',
    example: 120,
  })
  @IsNumber()
  activeAccounts: number;

  @ApiProperty({
    description: 'Số tài khoản affiliate đang chờ duyệt',
    example: 15,
  })
  @IsNumber()
  pendingAccounts: number;

  @ApiProperty({
    description: 'Số tài khoản affiliate bị khóa',
    example: 15,
  })
  @IsNumber()
  blockedAccounts: number;

  @ApiProperty({
    description: 'Tổng doanh thu từ affiliate',
    example: ********,
  })
  @IsNumber()
  totalRevenue: number;

  @ApiProperty({
    description: 'Tổng hoa hồng đã trả',
    example: 3750000,
  })
  @IsNumber()
  totalCommission: number;

  @ApiProperty({
    description: 'Tổng số lượt click',
    example: 25000,
  })
  @IsNumber()
  totalClicks: number;

  @ApiProperty({
    description: 'Tỷ lệ chuyển đổi trung bình',
    example: 4.8,
  })
  @IsNumber()
  averageConversionRate: number;
}
