import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { BullModule, getQueueToken } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Queue } from 'bull';
import { QueueName, EmailJobName, DEFAULT_JOB_OPTIONS } from '../queue.constants';
import { QueueService } from '../queue.service';

/**
 * File test này kiểm tra kết nối thực tế với Redis và khả năng tạo job
 * Để chạy test này, đảm bảo Redis đã được khởi động và biến môi trường REDIS_URL đã được cấu hình
 * 
 * Chạy bằng lệnh: npx jest src/shared/queue/tests/queue.test.ts --forceExit
 */
describe('Queue Integration Test', () => {
  let app: INestApplication;
  let queueService: QueueService;
  let emailQueue: Queue;

  // Thiết lập môi trường test
  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        // Tải Config từ biến môi trường
        ConfigModule.forRoot(),
        
        // Cấu hình Bull với Redis
        BullModule.forRootAsync({
          imports: [ConfigModule],
          useFactory: (configService: ConfigService) => ({
            redis: configService.get<string>('REDIS_URL'),
          }),
          inject: [ConfigService],
        }),
        
        // Đăng ký các queue
        BullModule.registerQueue(
          { name: QueueName.EMAIL },
          { name: QueueName.SMS },
          { name: QueueName.NOTIFICATION },
          { name: QueueName.DATA_PROCESS }
        ),
      ],
      providers: [QueueService],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
    
    // Lấy các service và queue
    queueService = moduleFixture.get<QueueService>(QueueService);
    emailQueue = moduleFixture.get<Queue>(getQueueToken(QueueName.EMAIL));

    // Xóa tất cả job hiện có
    await emailQueue.empty();
    console.log('Đã xóa tất cả job hiện có trong queue email');
  });

  afterAll(async () => {
    // Đóng kết nối
    await app.close();
  });

  // Test kết nối tới Redis
  it('Kiểm tra kết nối Redis', async () => {
    // Kiểm tra xem đã kết nối được đến Redis chưa
    expect(emailQueue).toBeDefined();
    
    // Lấy thông tin về Redis client
    const client = await emailQueue.client;
    
    // Kiểm tra kết nối Redis
    expect(client.status).toBe('ready');
    console.log('Đã kết nối thành công đến Redis');
  });

  // Test thêm job vào queue
  it('Thêm job vào email queue', async () => {
    // Dữ liệu test
    const testEmail = {
      to: '<EMAIL>',
      subject: 'Test Email',
      content: 'This is a test email',
      timestamp: Date.now(),
    };

    // Thêm job vào queue
    const jobId = await queueService.addEmailJob(testEmail);
    console.log(`Job đã được thêm vào queue với ID: ${jobId}`);
    
    // Lấy job từ queue để kiểm tra
    const job = await emailQueue.getJob(jobId);
    
    // Kiểm tra job đã được thêm chưa
    expect(job).toBeDefined();
    if (!job) {
      fail('Job không được tìm thấy'); // Sử dụng fail để dừng test nếu job là null
      return;
    }
    
    expect(job.id).toBe(jobId);
    expect(job.data).toEqual(testEmail);
    expect(job.name).toBe(EmailJobName.SEND_EMAIL);
    
    console.log('Job data:', job.data);
  });

  // Test thêm job vào queue với tùy chọn
  it('Thêm template email job với priority cao', async () => {
    // Dữ liệu test
    const templateData = {
      to: '<EMAIL>',
      templateId: 'welcome-email',
      data: { name: 'Test User', activationLink: 'https://example.com/activate' },
      timestamp: Date.now(),
    };

    // Thêm job vào queue với priority cao
    const jobId = await queueService.addTemplateEmailJob(templateData);
    console.log(`Template job đã được thêm vào queue với ID: ${jobId}`);
    
    // Lấy job từ queue để kiểm tra
    const job = await emailQueue.getJob(jobId);
    
    // Kiểm tra job đã được thêm chưa
    expect(job).toBeDefined();
    if (!job) {
      fail('Template job không được tìm thấy');
      return;
    }
    
    expect(job.id).toBe(jobId);
    expect(job.data).toEqual(templateData);
    expect(job.name).toBe(EmailJobName.SEND_TEMPLATE_EMAIL);
    
    // Kiểm tra priority của job
    const jobState = await job.getState();
    console.log(`Trạng thái job: ${jobState}, Priority: ${job.opts.priority}`);
    expect(job.opts.priority).toBe(1); // Priority cao hơn
    
    console.log('Template job data:', job.data);
  });

  // Test lấy trạng thái của job
  it('Lấy trạng thái của job từ queue', async () => {
    // Thêm job mới để test
    const testData = {
      to: '<EMAIL>',
      subject: 'Status Test',
      content: 'Testing job status',
      timestamp: Date.now(),
    };
    
    // Thêm job vào queue
    const jobId = await queueService.addEmailJob(testData);
    console.log(`Job status test được thêm vào queue với ID: ${jobId}`);
    
    // Lấy trạng thái job
    const status = await queueService.getJobStatus(QueueName.EMAIL, jobId);
    
    // Kiểm tra thông tin trạng thái
    expect(status).toBeDefined();
    expect(status.id).toBe(jobId);
    expect(status.data).toEqual(testData);
    expect(status.state).toBeDefined();
    
    console.log(`Trạng thái của job ${jobId}: ${status.state}`);
    console.log('Chi tiết trạng thái:', status);
  });
}); 