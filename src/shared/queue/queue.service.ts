import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue, Job } from 'bull';
import { AppException, ErrorCode } from '@/common';
import { 
  QueueName, 
  EmailJobName, 
  SmsJobName, 
  NotificationJobName, 
  DEFAULT_JOB_OPTIONS, 
  HIGH_PRIORITY_JOB_OPTIONS 
} from './queue.constants';
import { EmailJobData, TemplateEmailJobData, JobOptions } from './queue.types';

/**
 * Job names for system email queue
 */
export enum SystemEmailJobName {
  SEND_SYSTEM_EMAIL = 'send-system-email',
  SEND_TEMPLATE_EMAIL = 'send-template-email'
}

/**
 * Service quản lý việc thêm job vào các queue
 */
@Injectable()
export class QueueService {
  private readonly logger = new Logger(QueueService.name);

  constructor(
    @InjectQueue(QueueName.EMAIL) private readonly emailQueue: Queue,
    @InjectQueue(QueueName.SMS) private readonly smsQueue: Queue,
    @InjectQueue(QueueName.NOTIFICATION) private readonly notificationQueue: Queue,
    @InjectQueue(QueueName.DATA_PROCESS) private readonly dataProcessQueue: Queue,
    @InjectQueue(QueueName.SEND_SYSTEM_EMAIL) private readonly systemEmailQueue: Queue,
  ) {}

  /**
   * Thêm job vào queue email
   * @param data Dữ liệu email cần gửi
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addEmailJob(data: EmailJobData, opts?: JobOptions): Promise<string | number> {
    try {
      const job = await this.emailQueue.add(EmailJobName.SEND_EMAIL, data, opts);
      this.logger.log(`Đã thêm job email vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job email vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job vào queue');
    }
  }

  /**
   * Thêm job gửi email theo mẫu vào queue
   * @param data Dữ liệu email mẫu cần gửi
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addTemplateEmailJob(data: TemplateEmailJobData, opts?: JobOptions): Promise<string | number> {
    try {
      const options = opts || HIGH_PRIORITY_JOB_OPTIONS;
      const job = await this.emailQueue.add(EmailJobName.SEND_TEMPLATE_EMAIL, data, options);
      this.logger.log(`Đã thêm job email mẫu vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job email mẫu vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job vào queue');
    }
  }

  /**
   * Thêm job gửi email hệ thống vào queue
   * Sử dụng cấu hình email mặc định từ biến môi trường
   * @param data Dữ liệu email cần gửi
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addSystemEmailJob(data: EmailJobData, opts?: JobOptions): Promise<string | number> {
    try {
      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.systemEmailQueue.add(SystemEmailJobName.SEND_SYSTEM_EMAIL, data, options);
      this.logger.log(`Đã thêm job email hệ thống vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job email hệ thống vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job email hệ thống vào queue');
    }
  }

  /**
   * Thêm job gửi email hệ thống theo mẫu vào queue
   * Sử dụng cấu hình email mặc định từ biến môi trường
   * @param data Dữ liệu email mẫu cần gửi
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addSystemTemplateEmailJob(data: TemplateEmailJobData, opts?: JobOptions): Promise<string | number> {
    try {
      const options = opts || HIGH_PRIORITY_JOB_OPTIONS;
      const job = await this.systemEmailQueue.add(SystemEmailJobName.SEND_TEMPLATE_EMAIL, data, options);
      this.logger.log(`Đã thêm job email mẫu hệ thống vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job email mẫu hệ thống vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job email mẫu hệ thống vào queue');
    }
  }

  /**
   * Thêm job vào queue SMS
   * @param data Dữ liệu job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addSmsJob(data: any, opts?: JobOptions): Promise<string | number> {
    try {
      const job = await this.smsQueue.add(SmsJobName.SEND_SMS, data, opts);
      this.logger.log(`Đã thêm job SMS vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job SMS vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job vào queue');
    }
  }

  /**
   * Thêm job vào queue thông báo
   * @param data Dữ liệu job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addNotificationJob(data: any, opts?: JobOptions): Promise<string | number> {
    try {
      const job = await this.notificationQueue.add(NotificationJobName.SEND_NOTIFICATION, data, opts);
      this.logger.log(`Đã thêm job thông báo vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job thông báo vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job vào queue');
    }
  }

  /**
   * Thêm job vào queue xử lý dữ liệu
   * @param jobName Tên job cần xử lý
   * @param data Dữ liệu job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addDataProcessJob(jobName: string, data: any, opts?: JobOptions): Promise<string | number> {
    try {
      const job = await this.dataProcessQueue.add(jobName, data, opts);
      this.logger.log(`Đã thêm job xử lý dữ liệu vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job xử lý dữ liệu vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job vào queue');
    }
  }

  /**
   * Kiểm tra trạng thái của job
   * @param queueName Tên queue
   * @param jobId ID của job
   * @returns Thông tin về job
   */
  async getJobStatus(queueName: string, jobId: string | number): Promise<any> {
    try {
      let queue: Queue;
      
      switch (queueName) {
        case QueueName.EMAIL:
          queue = this.emailQueue;
          break;
        case QueueName.SMS:
          queue = this.smsQueue;
          break;
        case QueueName.NOTIFICATION:
          queue = this.notificationQueue;
          break;
        case QueueName.DATA_PROCESS:
          queue = this.dataProcessQueue;
          break;
        case QueueName.SEND_SYSTEM_EMAIL:
          queue = this.systemEmailQueue;
          break;
        default:
          throw new AppException(ErrorCode.VALIDATION_ERROR, `Queue không tồn tại: ${queueName}`);
      }

      const job = await queue.getJob(jobId);
      
      if (!job) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, `Không tìm thấy job với ID: ${jobId}`);
      }

      const state = await job.getState();
      
      return {
        id: job.id,
        data: job.data,
        state,
        progress: job.progress(),
        attemptsMade: job.attemptsMade,
        failedReason: job.failedReason,
        stacktrace: job.stacktrace,
        timestamp: job.timestamp,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi kiểm tra trạng thái job: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể kiểm tra trạng thái job');
    }
  }
} 